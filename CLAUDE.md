# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Setup and Installation
```bash
# Initial setup (Python 3.12+ required)
python setup.py

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # For development dependencies
```

### Running the Application
```bash
# Main trading bot
python beast_mode_bot.py

# Launch dashboard
python launch_dashboard.py

# Deploy weather bot
python deploy_weather_bot.py
```

### Testing
```bash
# Interactive test runner (recommended)
python run_tests.py
# Options: 1=Quick tests (30s), 2=Full tests (2-3min), 3=Custom tests

# Direct pytest commands
python -m pytest tests/ -v --tb=short -s
python -m pytest tests/test_end_to_e2e.py -v -s  # End-to-end tests
```

### Code Quality
```bash
# Format code
black src/
isort src/

# Type checking
mypy src/
```

### Performance Analysis
```bash
python performance_analysis.py        # Main performance analysis
python portfolio_health_check.py     # Portfolio health check
python cost_monitor.py               # Cost monitoring
```

## Project Architecture

This is a **Kalshi AI Trading Bot** with specialized weather derivatives trading capabilities. The system uses a multi-agent AI approach with Forecaster, Critic, and Trader agents for decision-making.

### Core Structure
- `src/clients/`: API integrations (Kalshi, xAI/Grok-4, OpenAI)
- `src/strategies/`: Trading strategies (unified system, weather-specific, market making)
- `src/jobs/`: Trading execution pipeline (ingest, decide, execute, track)
- `src/utils/`: Core utilities (database, logging, security)
- `src/weather/`: Weather trading components and NOAA integration

### Key Technologies
- **AI Models**: Primary Grok-4 via xAI API, OpenAI fallback
- **Database**: SQLite with SQLAlchemy ORM
- **UI**: Streamlit dashboard with real-time updates
- **Weather Data**: NOAA/NWS integration via weather APIs

### Trading Configuration
- Maximum 5% position size per trade
- Maximum 15% daily loss limit  
- Minimum 50% confidence threshold for trades
- Kelly Criterion for position sizing
- 7 weather stations monitored (NYC, Miami, Chicago, Denver, Austin, LA, Philadelphia)

### Database Schema
The system uses SQLite with tables for positions, trades, market_data, weather_data, and ai_decisions. Database utilities are in `src/utils/database.py`.

## Development Guidelines

### Code Standards
- PEP 8 compliance with 88-character line limit
- Type hints required for all functions
- Google-style docstrings
- Black formatter and isort for import sorting
- Target 80%+ test coverage

### Security Requirements
- Never commit API keys - use environment variables
- Sensitive data stored in encrypted format
- All inputs must be validated
- Use `src/utils/security.py` for encryption utilities

### Testing Structure
- Comprehensive test suite in `tests/` directory
- Use pytest with async support (pytest-asyncio)
- Mock external APIs in tests using pytest fixtures
- End-to-end tests verify full trading pipeline

## Important Notes

- This is experimental software for educational/research purposes
- Requires Python 3.12+ for optimal performance
- Environment variables must be configured (see `env.template`)
- The system includes both general prediction market trading and specialized weather derivatives