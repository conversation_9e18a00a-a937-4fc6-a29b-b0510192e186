"""
Security validation utilities for the Kalshi trading system.
Provides comprehensive input validation, JSON schema validation, and security checks.
"""

import re
import json
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from jsonschema import validate, ValidationError, draft7_format_checker
import structlog

logger = structlog.get_logger(__name__)

# Financial constants for validation
MIN_PRICE = Decimal('0.01')  # 1 cent
MAX_PRICE = Decimal('0.99')  # 99 cents
MIN_QUANTITY = 1
MAX_QUANTITY = 10000
MIN_BALANCE = Decimal('1.00')
MAX_BALANCE = Decimal('1000000.00')  # 1M USD


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    is_valid: bool
    value: Any = None
    error_message: str = ""
    sanitized_value: Any = None


class SecurityValidator:
    """Comprehensive security validator for trading system inputs."""
    
    # JSON Schemas for AI responses
    TRADING_DECISION_SCHEMA = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "enum": ["BUY", "SELL", "SKIP"]
            },
            "confidence": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0
            },
            "rationale": {
                "type": "string",
                "maxLength": 1000
            },
            "expected_outcome": {
                "type": "string",
                "maxLength": 500
            },
            "risk_assessment": {
                "type": "string",
                "maxLength": 500
            },
            "quantity": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10000
            },
            "price_target": {
                "type": "number",
                "minimum": 0.01,
                "maximum": 0.99
            }
        },
        "required": ["action", "confidence"],
        "additionalProperties": False
    }
    
    PORTFOLIO_ANALYSIS_SCHEMA = {
        "type": "object",
        "properties": {
            "allocations": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "market_id": {"type": "string"},
                        "allocation": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                        "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0}
                    },
                    "required": ["market_id", "allocation", "confidence"]
                }
            },
            "total_allocation": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0
            }
        },
        "required": ["allocations"],
        "additionalProperties": False
    }

    @staticmethod
    def validate_json_response(data: Dict[str, Any], schema: Dict[str, Any]) -> ValidationResult:
        """Validate JSON response against schema."""
        try:
            validate(instance=data, schema=schema, format_checker=draft7_format_checker)
            return ValidationResult(is_valid=True, value=data, sanitized_value=data)
        except ValidationError as e:
            logger.warning("JSON schema validation failed", error=str(e), data_keys=list(data.keys()))
            return ValidationResult(
                is_valid=False, 
                error_message=f"JSON validation failed: {e.message}"
            )
        except Exception as e:
            logger.error("Unexpected JSON validation error", error=str(e))
            return ValidationResult(
                is_valid=False,
                error_message=f"JSON validation error: {str(e)}"
            )

    @staticmethod
    def validate_trading_decision(data: Dict[str, Any]) -> ValidationResult:
        """Validate AI trading decision response."""
        return SecurityValidator.validate_json_response(
            data, SecurityValidator.TRADING_DECISION_SCHEMA
        )

    @staticmethod
    def validate_portfolio_analysis(data: Dict[str, Any]) -> ValidationResult:
        """Validate AI portfolio analysis response."""
        return SecurityValidator.validate_json_response(
            data, SecurityValidator.PORTFOLIO_ANALYSIS_SCHEMA
        )

    @staticmethod
    def validate_price(price: Union[str, float, int, Decimal]) -> ValidationResult:
        """Safely validate and convert price inputs."""
        try:
            if isinstance(price, str):
                # Remove any non-numeric characters except decimal point
                price_clean = re.sub(r'[^\d.]', '', price)
                if not price_clean:
                    return ValidationResult(False, error_message="Price cannot be empty")
                price = Decimal(price_clean)
            elif isinstance(price, (int, float)):
                price = Decimal(str(price))
            elif not isinstance(price, Decimal):
                return ValidationResult(False, error_message="Price must be numeric")
            
            # Round to 2 decimal places (cents)
            price = price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
            if not MIN_PRICE <= price <= MAX_PRICE:
                return ValidationResult(
                    False, 
                    error_message=f"Price must be between ${MIN_PRICE} and ${MAX_PRICE}"
                )
            
            return ValidationResult(True, value=price, sanitized_value=price)
            
        except (InvalidOperation, ValueError) as e:
            return ValidationResult(False, error_message=f"Invalid price format: {e}")

    @staticmethod
    def validate_quantity(quantity: Union[str, int]) -> ValidationResult:
        """Validate trading quantity."""
        try:
            if isinstance(quantity, str):
                quantity_clean = re.sub(r'[^\d]', '', quantity)
                if not quantity_clean:
                    return ValidationResult(False, error_message="Quantity cannot be empty")
                quantity = int(quantity_clean)
            elif not isinstance(quantity, int):
                quantity = int(quantity)
            
            if not MIN_QUANTITY <= quantity <= MAX_QUANTITY:
                return ValidationResult(
                    False,
                    error_message=f"Quantity must be between {MIN_QUANTITY} and {MAX_QUANTITY}"
                )
            
            return ValidationResult(True, value=quantity, sanitized_value=quantity)
            
        except (ValueError, TypeError) as e:
            return ValidationResult(False, error_message=f"Invalid quantity format: {e}")

    @staticmethod
    def validate_balance(balance: Union[str, float, int, Decimal]) -> ValidationResult:
        """Validate account balance."""
        try:
            if isinstance(balance, str):
                balance_clean = re.sub(r'[^\d.]', '', balance)
                if not balance_clean:
                    return ValidationResult(False, error_message="Balance cannot be empty")
                balance = Decimal(balance_clean)
            elif isinstance(balance, (int, float)):
                balance = Decimal(str(balance))
            elif not isinstance(balance, Decimal):
                return ValidationResult(False, error_message="Balance must be numeric")
            
            balance = balance.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
            if not MIN_BALANCE <= balance <= MAX_BALANCE:
                return ValidationResult(
                    False,
                    error_message=f"Balance must be between ${MIN_BALANCE} and ${MAX_BALANCE}"
                )
            
            return ValidationResult(True, value=balance, sanitized_value=balance)
            
        except (InvalidOperation, ValueError) as e:
            return ValidationResult(False, error_message=f"Invalid balance format: {e}")

    @staticmethod
    def validate_market_id(market_id: str) -> ValidationResult:
        """Validate market ID format."""
        if not isinstance(market_id, str):
            return ValidationResult(False, error_message="Market ID must be a string")
        
        # Sanitize: remove any non-alphanumeric characters except hyphens and underscores
        market_id_clean = re.sub(r'[^a-zA-Z0-9\-_]', '', market_id)
        
        if not market_id_clean:
            return ValidationResult(False, error_message="Market ID cannot be empty")
        
        if len(market_id_clean) > 100:
            return ValidationResult(False, error_message="Market ID too long")
        
        return ValidationResult(True, value=market_id_clean, sanitized_value=market_id_clean)

    @staticmethod
    def validate_trading_side(side: str) -> ValidationResult:
        """Validate trading side (BUY/SELL)."""
        if not isinstance(side, str):
            return ValidationResult(False, error_message="Side must be a string")
        
        side_clean = side.upper().strip()
        
        if side_clean not in ['BUY', 'SELL']:
            return ValidationResult(False, error_message="Side must be 'BUY' or 'SELL'")
        
        return ValidationResult(True, value=side_clean, sanitized_value=side_clean)

    @staticmethod
    def validate_confidence(confidence: Union[float, int, str]) -> ValidationResult:
        """Validate confidence score (0.0 to 1.0)."""
        try:
            if isinstance(confidence, str):
                confidence = float(confidence)
            elif isinstance(confidence, int):
                confidence = float(confidence)
            elif not isinstance(confidence, float):
                return ValidationResult(False, error_message="Confidence must be numeric")
            
            if not 0.0 <= confidence <= 1.0:
                return ValidationResult(False, error_message="Confidence must be between 0.0 and 1.0")
            
            return ValidationResult(True, value=confidence, sanitized_value=confidence)
            
        except (ValueError, TypeError) as e:
            return ValidationResult(False, error_message=f"Invalid confidence format: {e}")

    @staticmethod
    def sanitize_text(text: str, max_length: int = 1000) -> ValidationResult:
        """Sanitize text input for logging and storage."""
        if not isinstance(text, str):
            return ValidationResult(False, error_message="Text must be a string")
        
        # Remove any control characters and limit length
        text_clean = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        text_clean = text_clean.strip()[:max_length]
        
        return ValidationResult(True, value=text_clean, sanitized_value=text_clean)

    @staticmethod
    def validate_file_path(file_path: str, allowed_extensions: Optional[List[str]] = None) -> ValidationResult:
        """Validate file path to prevent directory traversal."""
        if not isinstance(file_path, str):
            return ValidationResult(False, error_message="File path must be a string")
        
        # Check for directory traversal attempts
        if '..' in file_path or file_path.startswith('/') or '\\' in file_path:
            return ValidationResult(False, error_message="Invalid file path")
        
        # Check file extension if specified
        if allowed_extensions:
            file_ext = file_path.lower().split('.')[-1] if '.' in file_path else ''
            if file_ext not in allowed_extensions:
                return ValidationResult(
                    False, 
                    error_message=f"File extension must be one of: {allowed_extensions}"
                )
        
        return ValidationResult(True, value=file_path, sanitized_value=file_path)


class FinancialCalculator:
    """Secure financial calculations using Decimal arithmetic."""
    
    @staticmethod
    def calculate_position_value(price: Decimal, quantity: int) -> ValidationResult:
        """Calculate position value with overflow protection."""
        try:
            if price <= 0 or quantity <= 0:
                return ValidationResult(False, error_message="Price and quantity must be positive")
            
            value = price * Decimal(str(quantity))
            
            # Check for unreasonable values
            if value > Decimal('100000.00'):  # $100k position limit
                return ValidationResult(False, error_message="Position value exceeds maximum limit")
            
            return ValidationResult(True, value=value, sanitized_value=value)
            
        except Exception as e:
            return ValidationResult(False, error_message=f"Calculation error: {e}")

    @staticmethod
    def calculate_kelly_position_size(
        balance: Decimal,
        win_probability: float,
        win_amount: Decimal,
        loss_amount: Decimal,
        kelly_fraction: float = 0.25
    ) -> ValidationResult:
        """Calculate Kelly Criterion position size with safety limits."""
        try:
            if not (0 < win_probability < 1):
                return ValidationResult(False, error_message="Win probability must be between 0 and 1")
            
            if kelly_fraction <= 0 or kelly_fraction > 1:
                return ValidationResult(False, error_message="Kelly fraction must be between 0 and 1")
            
            # Kelly formula: f = (bp - q) / b
            # Where: b = odds, p = win probability, q = loss probability
            odds = win_amount / loss_amount if loss_amount > 0 else Decimal('1')
            kelly_percentage = (odds * Decimal(str(win_probability)) - Decimal(str(1 - win_probability))) / odds
            
            # Apply fractional Kelly for safety
            kelly_percentage *= Decimal(str(kelly_fraction))
            
            # Ensure positive and reasonable
            if kelly_percentage <= 0:
                return ValidationResult(True, value=Decimal('0'), sanitized_value=Decimal('0'))
            
            # Cap at maximum position size (5% of balance)
            max_position = balance * Decimal('0.05')
            position_size = min(balance * kelly_percentage, max_position)
            
            return ValidationResult(True, value=position_size, sanitized_value=position_size)
            
        except Exception as e:
            return ValidationResult(False, error_message=f"Kelly calculation error: {e}")

    @staticmethod
    def validate_risk_limits(
        position_value: Decimal,
        total_portfolio_value: Decimal,
        max_position_pct: float = 0.05
    ) -> ValidationResult:
        """Validate position against risk limits."""
        try:
            if total_portfolio_value <= 0:
                return ValidationResult(False, error_message="Portfolio value must be positive")
            
            position_percentage = position_value / total_portfolio_value
            
            if position_percentage > Decimal(str(max_position_pct)):
                return ValidationResult(
                    False,
                    error_message=f"Position exceeds {max_position_pct*100}% portfolio limit"
                )
            
            return ValidationResult(True, value=position_percentage, sanitized_value=position_percentage)
            
        except Exception as e:
            return ValidationResult(False, error_message=f"Risk validation error: {e}")


# Convenience functions for common validations
def safe_parse_json(json_str: str, schema: Dict[str, Any]) -> ValidationResult:
    """Safely parse and validate JSON string."""
    try:
        data = json.loads(json_str)
        return SecurityValidator.validate_json_response(data, schema)
    except json.JSONDecodeError as e:
        return ValidationResult(False, error_message=f"Invalid JSON: {e}")


def validate_ai_trading_decision(json_str: str) -> ValidationResult:
    """Validate AI trading decision JSON."""
    result = safe_parse_json(json_str, SecurityValidator.TRADING_DECISION_SCHEMA)
    if result.is_valid:
        logger.info("AI trading decision validated successfully")
    else:
        logger.warning("AI trading decision validation failed", error=result.error_message)
    return result


def validate_trading_parameters(
    market_id: str,
    side: str,
    quantity: int,
    price: Union[str, float, Decimal]
) -> ValidationResult:
    """Validate complete trading parameters."""
    
    # Validate each parameter
    market_result = SecurityValidator.validate_market_id(market_id)
    if not market_result.is_valid:
        return market_result
    
    side_result = SecurityValidator.validate_trading_side(side)
    if not side_result.is_valid:
        return side_result
    
    quantity_result = SecurityValidator.validate_quantity(quantity)
    if not quantity_result.is_valid:
        return quantity_result
    
    price_result = SecurityValidator.validate_price(price)
    if not price_result.is_valid:
        return price_result
    
    # All validations passed
    return ValidationResult(
        True,
        value={
            'market_id': market_result.sanitized_value,
            'side': side_result.sanitized_value,
            'quantity': quantity_result.sanitized_value,
            'price': price_result.sanitized_value
        },
        sanitized_value={
            'market_id': market_result.sanitized_value,
            'side': side_result.sanitized_value,
            'quantity': quantity_result.sanitized_value,
            'price': price_result.sanitized_value
        }
    )