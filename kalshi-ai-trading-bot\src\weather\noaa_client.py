"""
NOAA Data Client - Minimal adapter leveraging existing weather libraries

This client reuses functionality from established libraries to avoid reinventing the wheel:
- python-metar: METAR parsing
- siphon: NOAA model data access
- metpy: Meteorological calculations
- requests: Direct API calls for AFDs and CLI reports
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import re

import requests
from metar.Metar import Metar
from siphon.catalog import TDSCatalog
from siphon.ncss import NCSS
import numpy as np
import pandas as pd
from metpy.units import units
import metpy.calc as mpcalc

from src.utils.logging_setup import get_trading_logger


@dataclass
class WeatherStation:
    """Weather station metadata and quirks."""
    station_id: str  # KNYC, KMIA, etc.
    name: str
    latitude: float
    longitude: float
    elevation: int  # meters
    timezone: str
    wfo_code: str  # Weather Forecast Office code
    quirks: List[str]  # Known sensor issues
    nearby_stations: List[str]  # For validation


@dataclass
class WeatherObservation:
    """Parsed METAR observation."""
    station_id: str
    observation_time: datetime
    temperature: float  # Celsius
    temperature_f: float  # Fahrenheit
    dewpoint: float
    wind_speed: float  # knots
    wind_direction: int
    pressure: float  # mb
    visibility: float  # miles
    sky_conditions: str
    raw_metar: str
    six_hour_max: Optional[float] = None
    six_hour_min: Optional[float] = None
    twenty_four_hour_max: Optional[float] = None
    twenty_four_hour_min: Optional[float] = None


@dataclass
class AFDAnalysis:
    """Parsed Area Forecast Discussion."""
    office_code: str
    issue_time: datetime
    confidence_score: float  # 0-100 extracted by LLM
    model_preferences: Dict[str, float]
    concerns: List[str]
    pattern_type: str
    raw_text: str


@dataclass
class ModelForecast:
    """Model forecast data point."""
    model_name: str  # HRRR, NAM, GFS, etc.
    valid_time: datetime
    temperature: float  # Fahrenheit
    dewpoint: float
    wind_speed: float
    wind_direction: int
    cloud_cover: float  # percentage
    precipitation_chance: float


class NOAAClient:
    """
    Minimal NOAA data adapter that leverages existing libraries.
    
    Key principle: Don't reinvent the wheel - use established packages!
    """
    
    # Weather station definitions for Kalshi markets
    WEATHER_STATIONS = {
        'KNYC': WeatherStation(
            station_id='KNYC',
            name='New York Central Park',
            latitude=40.7789,
            longitude=-73.9692,
            elevation=47,
            timezone='America/New_York',
            wfo_code='OKX',
            quirks=['2-3F cool bias vs city', 'occasional sensor spikes'],
            nearby_stations=['KLGA', 'KEWR', 'KJFK']
        ),
        'KMIA': WeatherStation(
            station_id='KMIA',
            name='Miami International',
            latitude=25.7931,
            longitude=-80.2906,
            elevation=2,
            timezone='America/New_York',
            wfo_code='MFL',
            quirks=['Most reliable sensor', 'Sea breeze timing critical'],
            nearby_stations=['KFLL', 'KOPF', 'KHWO']
        ),
        'KMDW': WeatherStation(
            station_id='KMDW',
            name='Chicago Midway',
            latitude=41.7867,
            longitude=-87.7522,
            elevation=188,
            timezone='America/Chicago',
            wfo_code='LOT',
            quirks=['Lake breeze can drop 10F', 'Construction 2022-2024'],
            nearby_stations=['KORD', 'KIGQ', 'KGYY']
        ),
        'KDEN': WeatherStation(
            station_id='KDEN',
            name='Denver International',
            latitude=39.8561,
            longitude=-104.6737,
            elevation=1656,
            timezone='America/Denver',
            wfo_code='BOU',
            quirks=['Most volatile', 'Chinook winds', 'Elevation effects'],
            nearby_stations=['KAPA', 'KBJC', 'KFTG']
        ),
        'KAUS': WeatherStation(
            station_id='KAUS',
            name='Austin Bergstrom',
            latitude=30.1945,
            longitude=-97.6699,
            elevation=165,
            timezone='America/Chicago',
            wfo_code='EWX',
            quirks=['Airport south of city', 'Morning min predicts max'],
            nearby_stations=['KATT', 'KGTU', 'KHYI']
        ),
        'KLAX': WeatherStation(
            station_id='KLAX',
            name='Los Angeles International',
            latitude=33.9425,
            longitude=-118.4081,
            elevation=38,
            timezone='America/Los_Angeles',
            wfo_code='LOX',
            quirks=['Marine layer critical', 'June gloom', 'Santa Ana winds'],
            nearby_stations=['KSMO', 'KHHR', 'KLGB']
        ),
        'KPHL': WeatherStation(
            station_id='KPHL',
            name='Philadelphia International',
            latitude=39.8719,
            longitude=-75.2411,
            elevation=11,
            timezone='America/New_York',
            wfo_code='PHI',
            quirks=['Urban heat stronger than NYC', 'Sensor relocated 2022'],
            nearby_stations=['KPNE', 'KTTN', 'KWRI']
        )
    }
    
    def __init__(self):
        self.logger = get_trading_logger('noaa_client')
        self.metar_base_url = "https://aviationweather.gov/api/data/metar"
        self.afd_base_url = "https://api.weather.gov/products"
        self.cli_base_url = "https://www.weather.gov/wrh/climate"
        
    async def get_current_observation(self, station_id: str) -> Optional[WeatherObservation]:
        """
        Get current METAR observation using python-metar library.
        
        This is real-time data updated every hour (sometimes more frequently).
        """
        try:
            # Fetch METAR from NOAA
            params = {
                'ids': station_id,
                'format': 'raw',
                'taf': 'false'
            }
            response = requests.get(self.metar_base_url, params=params)
            
            if response.status_code != 200:
                self.logger.error(f"Failed to fetch METAR for {station_id}: {response.status_code}")
                return None
            
            raw_metar = response.text.strip()
            if not raw_metar or 'No data' in raw_metar:
                self.logger.warning(f"No METAR data available for {station_id}")
                return None
            
            # Parse using python-metar library
            obs = Metar(raw_metar)
            
            # Extract temperature extremes if available (in remarks)
            six_hour_max = self._extract_temp_extreme(raw_metar, 'T1')
            six_hour_min = self._extract_temp_extreme(raw_metar, 'T2')
            twenty_four_hour_max = self._extract_temp_extreme(raw_metar, '401')
            twenty_four_hour_min = self._extract_temp_extreme(raw_metar, '402')
            
            return WeatherObservation(
                station_id=station_id,
                observation_time=obs.time,
                temperature=obs.temp.value() if obs.temp else None,
                temperature_f=obs.temp.value('F') if obs.temp else None,
                dewpoint=obs.dewpt.value() if obs.dewpt else None,
                wind_speed=obs.wind_speed.value('KT') if obs.wind_speed else 0,
                wind_direction=obs.wind_dir.value() if obs.wind_dir else 0,
                pressure=obs.press.value('MB') if obs.press else None,
                visibility=obs.vis.value('SM') if obs.vis else None,
                sky_conditions=self._format_sky_conditions(obs),
                raw_metar=raw_metar,
                six_hour_max=six_hour_max,
                six_hour_min=six_hour_min,
                twenty_four_hour_max=twenty_four_hour_max,
                twenty_four_hour_min=twenty_four_hour_min
            )
            
        except Exception as e:
            self.logger.error(f"Error parsing METAR for {station_id}: {e}")
            return None
    
    async def get_afd(self, wfo_code: str) -> Optional[AFDAnalysis]:
        """
        Get Area Forecast Discussion from NWS API.
        
        AFDs are issued 4 times daily and contain meteorologist analysis.
        """
        try:
            # Get list of products from WFO
            url = f"{self.afd_base_url}?office={wfo_code}&type=AFD"
            response = requests.get(url)
            
            if response.status_code != 200:
                self.logger.error(f"Failed to fetch AFD list for {wfo_code}: {response.status_code}")
                return None
            
            products = response.json().get('products', [])
            if not products:
                self.logger.warning(f"No AFD products available for {wfo_code}")
                return None
            
            # Get the most recent AFD
            latest_product_id = products[0]
            afd_url = f"{self.afd_base_url}/{latest_product_id}"
            afd_response = requests.get(afd_url)
            
            if afd_response.status_code != 200:
                self.logger.error(f"Failed to fetch AFD {latest_product_id}")
                return None
            
            afd_data = afd_response.json()
            raw_text = afd_data.get('productText', '')
            issue_time = datetime.fromisoformat(afd_data.get('issuanceTime', '').replace('Z', '+00:00'))
            
            # Extract confidence and model preferences (simplified - would use LLM in production)
            confidence_score = self._extract_confidence_from_afd(raw_text)
            model_preferences = self._extract_model_preferences(raw_text)
            concerns = self._extract_concerns(raw_text)
            pattern_type = self._identify_pattern_type(raw_text)
            
            return AFDAnalysis(
                office_code=wfo_code,
                issue_time=issue_time,
                confidence_score=confidence_score,
                model_preferences=model_preferences,
                concerns=concerns,
                pattern_type=pattern_type,
                raw_text=raw_text
            )
            
        except Exception as e:
            self.logger.error(f"Error fetching AFD for {wfo_code}: {e}")
            return None
    
    async def get_model_forecast(self, station: WeatherStation, model: str = 'HRRR') -> List[ModelForecast]:
        """
        Get model forecast data using Siphon library.
        
        Supports: HRRR, NAM, GFS, RAP
        """
        try:
            # Model catalog URLs
            model_urls = {
                'HRRR': 'https://thredds.ucar.edu/thredds/catalog/grib/NCEP/HRRR/CONUS_3km/surface/latest.xml',
                'NAM': 'https://thredds.ucar.edu/thredds/catalog/grib/NCEP/NAM/CONUS_12km/latest.xml',
                'GFS': 'https://thredds.ucar.edu/thredds/catalog/grib/NCEP/GFS/Global_0p25deg/latest.xml',
                'RAP': 'https://thredds.ucar.edu/thredds/catalog/grib/NCEP/RAP/CONUS_13km/latest.xml'
            }
            
            if model not in model_urls:
                self.logger.error(f"Unsupported model: {model}")
                return []
            
            # Access the catalog
            catalog = TDSCatalog(model_urls[model])
            
            # Get the latest dataset
            dataset_name = list(catalog.datasets.keys())[0]
            dataset = catalog.datasets[dataset_name]
            
            # Create NCSS query
            ncss = dataset.subset()
            query = ncss.query()
            
            # Set location
            query.lonlat_point(station.longitude, station.latitude)
            
            # Request temperature, dewpoint, wind variables
            query.variables('Temperature_height_above_ground',
                          'Dewpoint_temperature_height_above_ground',
                          'u-component_of_wind_height_above_ground',
                          'v-component_of_wind_height_above_ground')
            
            # Time range (next 24 hours)
            query.time_range(datetime.utcnow(), datetime.utcnow() + timedelta(hours=24))
            
            # Get data
            data = ncss.get_data(query)
            
            # Parse into forecast objects
            forecasts = []
            temps = data.variables['Temperature_height_above_ground'][:]
            times = data.variables['time'][:]
            
            for i, time_val in enumerate(times):
                valid_time = datetime.utcfromtimestamp(time_val)
                
                # Convert temperature from Kelvin to Fahrenheit
                temp_f = (temps[i] - 273.15) * 9/5 + 32
                
                forecasts.append(ModelForecast(
                    model_name=model,
                    valid_time=valid_time,
                    temperature=temp_f,
                    dewpoint=0,  # Would extract similarly
                    wind_speed=0,  # Would calculate from u/v components
                    wind_direction=0,
                    cloud_cover=0,
                    precipitation_chance=0
                ))
            
            return forecasts
            
        except Exception as e:
            self.logger.error(f"Error fetching {model} forecast: {e}")
            return []
    
    async def get_cli_report(self, station_id: str) -> Optional[Dict]:
        """
        Get Daily Climate Report (CLI) for settlement verification.
        
        This contains the official maximum temperature used for settlement.
        """
        try:
            station = self.WEATHER_STATIONS.get(station_id)
            if not station:
                self.logger.error(f"Unknown station: {station_id}")
                return None
            
            # CLI reports are typically available after 7 AM local time
            # This would need proper HTML parsing in production
            url = f"{self.cli_base_url}?wfo={station.wfo_code}"
            
            # Simplified - would parse actual CLI report
            return {
                'station_id': station_id,
                'report_date': datetime.now().date(),
                'max_temperature': None,  # Would extract from CLI
                'min_temperature': None,
                'precipitation': None,
                'report_time': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching CLI for {station_id}: {e}")
            return None
    
    def _extract_temp_extreme(self, metar: str, indicator: str) -> Optional[float]:
        """Extract temperature extremes from METAR remarks."""
        pattern = rf'{indicator}(\d{{3,4}})'
        match = re.search(pattern, metar)
        if match:
            value = int(match.group(1))
            # Convert from tenths of Celsius to Fahrenheit
            celsius = value / 10.0
            if indicator.startswith('T2') or indicator.startswith('402'):
                celsius = -celsius  # Negative temperatures
            return celsius * 9/5 + 32
        return None
    
    def _format_sky_conditions(self, obs: Metar) -> str:
        """Format sky conditions from METAR."""
        if not obs.sky:
            return "Unknown"
        
        conditions = []
        for layer in obs.sky:
            cover, height, cloud = layer
            conditions.append(f"{cover} at {height}")
        
        return ", ".join(conditions) if conditions else "Clear"
    
    def _extract_confidence_from_afd(self, text: str) -> float:
        """
        Extract confidence level from AFD text.
        
        In production, this would use the LLM for sophisticated analysis.
        For now, using keyword matching as a simplified approach.
        """
        text_lower = text.lower()
        
        # High confidence indicators
        if any(phrase in text_lower for phrase in ['high confidence', 'confident', 'good agreement']):
            return 80.0
        # Low confidence indicators
        elif any(phrase in text_lower for phrase in ['low confidence', 'uncertain', 'poor agreement']):
            return 40.0
        # Moderate confidence (default)
        else:
            return 60.0
    
    def _extract_model_preferences(self, text: str) -> Dict[str, float]:
        """
        Extract model preferences from AFD.
        
        Simplified version - production would use LLM.
        """
        preferences = {}
        text_upper = text.upper()
        
        models = ['NAM', 'GFS', 'ECMWF', 'HRRR', 'RAP', 'NBM']
        for model in models:
            if model in text_upper:
                # Simple scoring based on mentions
                preferences[model] = 1.0
        
        return preferences
    
    def _extract_concerns(self, text: str) -> List[str]:
        """
        Extract meteorologist concerns from AFD.
        
        Simplified - production would use LLM for sophisticated extraction.
        """
        concerns = []
        text_lower = text.lower()
        
        concern_keywords = [
            'uncertainty', 'concern', 'challenge', 'difficult',
            'question', 'issue', 'problem', 'tricky'
        ]
        
        for keyword in concern_keywords:
            if keyword in text_lower:
                concerns.append(f"Meteorologist expressed {keyword}")
        
        return concerns
    
    def _identify_pattern_type(self, text: str) -> str:
        """
        Identify weather pattern from AFD.
        
        Simplified pattern matching.
        """
        text_lower = text.lower()
        
        if 'high pressure' in text_lower:
            return 'high_pressure'
        elif 'low pressure' in text_lower:
            return 'low_pressure'
        elif 'front' in text_lower:
            return 'frontal'
        elif 'trough' in text_lower:
            return 'trough'
        elif 'ridge' in text_lower:
            return 'ridge'
        else:
            return 'mixed'
    
    async def get_all_station_observations(self) -> Dict[str, WeatherObservation]:
        """Get observations for all Kalshi weather stations."""
        observations = {}
        
        for station_id in self.WEATHER_STATIONS.keys():
            obs = await self.get_current_observation(station_id)
            if obs:
                observations[station_id] = obs
                self.logger.info(f"✅ {station_id}: {obs.temperature_f:.1f}°F")
        
        return observations
    
    async def get_model_consensus(self, station: WeatherStation, hour: int = 12) -> Dict[str, float]:
        """
        Get temperature forecast consensus from multiple models.
        
        Args:
            station: Weather station
            hour: Forecast hour (e.g., 12 for 12 hours ahead)
        
        Returns:
            Dictionary of model name to temperature forecast
        """
        consensus = {}
        
        # In production, would fetch from all models
        # For now, return mock data for testing
        models = ['HRRR', 'NAM', 'GFS', 'NBM']
        base_temp = 70.0  # Mock base temperature
        
        for model in models:
            # Add some variation between models
            variation = np.random.normal(0, 2)
            consensus[model] = base_temp + variation
        
        return consensus


# Quick test function
async def test_noaa_client():
    """Test NOAA client functionality."""
    client = NOAAClient()
    
    # Test METAR
    print("Testing METAR retrieval...")
    obs = await client.get_current_observation('KNYC')
    if obs:
        print(f"✅ KNYC Current: {obs.temperature_f:.1f}°F, {obs.sky_conditions}")
    
    # Test AFD
    print("\nTesting AFD retrieval...")
    afd = await client.get_afd('OKX')
    if afd:
        print(f"✅ AFD Confidence: {afd.confidence_score:.0f}%, Pattern: {afd.pattern_type}")
    
    # Test all stations
    print("\nTesting all stations...")
    observations = await client.get_all_station_observations()
    print(f"✅ Retrieved {len(observations)} station observations")


if __name__ == "__main__":
    # Run test
    asyncio.run(test_noaa_client())
