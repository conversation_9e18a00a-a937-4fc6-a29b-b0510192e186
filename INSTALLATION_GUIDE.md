# Weather Trading Bot v2.0 - Installation Guide

## 📋 Prerequisites

- Python 3.9 or higher (tested with Python 3.13)
- pip package manager
- Git (for cloning repositories)
- Windows: Visual C++ Build Tools (for some packages)
- Linux/Mac: gcc compiler (usually pre-installed)

## 🚀 Quick Installation

### Step 1: Clone the Repository
```bash
git clone https://github.com/ryanfrigo/kalshi-ai-trading-bot.git
cd kalshi-ai-trading-bot
```

### Step 2: Create Virtual Environment (Recommended)
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### Step 3: Install Core Requirements
```bash
# Install all required packages
pip install -r requirements.txt
```

## 📦 Installation Options

### Basic Installation (Trading Bot Only)
```bash
# Install core packages without optional features
pip install httpx aiohttp requests pandas numpy scipy \
    python-metar siphon metpy noaa-sdk pynws \
    python-dotenv pydantic cryptography
```

### Full Installation (All Features)
```bash
# Install everything including development tools
pip install -r requirements.txt
```

### Minimal Weather Installation
```bash
# Just the weather-specific packages
pip install python-metar siphon metpy noaa-sdk pynws \
    xarray netCDF4 diskcache
```

## 🔧 Platform-Specific Notes

### Windows Users
Some packages require additional steps:

1. **Install Visual C++ Build Tools**:
   - Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Install "Desktop development with C++"

2. **Cartopy Installation** (for weather maps):
   ```bash
   # Use conda for easier installation
   conda install -c conda-forge cartopy
   
   # Or use pre-built wheels
   pip install cartopy --no-binary cartopy
   ```

### Linux Users (Ubuntu/Debian)
Install system dependencies:
```bash
sudo apt-get update
sudo apt-get install -y \
    python3-dev \
    libgeos-dev \
    libproj-dev \
    libgdal-dev \
    gcc \
    g++
```

### macOS Users
Install system dependencies:
```bash
# Using Homebrew
brew install geos proj gdal
```

## 🔍 Verify Installation

### Test Core Packages
```python
# test_installation.py
import sys
print(f"Python version: {sys.version}")

# Test core packages
try:
    import pandas
    import numpy
    import scipy
    print("✅ Data analysis packages: OK")
except ImportError as e:
    print(f"❌ Data analysis packages: {e}")

# Test weather packages
try:
    from metar.Metar import Metar
    import siphon
    import metpy
    from noaa_sdk import NOAA
    print("✅ Weather packages: OK")
except ImportError as e:
    print(f"❌ Weather packages: {e}")

# Test trading packages
try:
    import httpx
    import aiohttp
    import cryptography
    print("✅ Trading packages: OK")
except ImportError as e:
    print(f"❌ Trading packages: {e}")

# Test AI packages
try:
    import openai
    print("✅ AI/LLM packages: OK")
except ImportError as e:
    print(f"❌ AI/LLM packages: {e}")
```

Run the test:
```bash
python test_installation.py
```

### Test Weather Data Access
```bash
# Test NOAA data retrieval
python test_weather_data.py
```

## 🔄 Updating Packages

### Update All Packages
```bash
pip install --upgrade -r requirements.txt
```

### Update Specific Package
```bash
pip install --upgrade package_name
```

### Check Outdated Packages
```bash
pip list --outdated
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. "No module named 'tkinter'"
**Solution**: Tkinter comes with Python but may need separate installation
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# macOS (should be included)
# Reinstall Python if missing

# Windows (should be included)
# Reinstall Python with tkinter option checked
```

#### 2. SSL Certificate Errors
**Solution**: Update certificates or use workaround
```python
# Add to your script (development only)
import ssl
ssl._create_default_https_context = ssl._create_unverified_context
```

#### 3. Cartopy Installation Fails
**Solution**: Use conda or pre-built wheels
```bash
# Option 1: Use conda
conda install -c conda-forge cartopy

# Option 2: Install without building
pip install cartopy --no-binary cartopy

# Option 3: Skip cartopy (it's optional)
# Comment out cartopy in requirements.txt
```

#### 4. Memory Issues During Installation
**Solution**: Install packages one at a time
```bash
# Install in batches
pip install pandas numpy scipy
pip install python-metar siphon metpy
pip install noaa-sdk pynws
```

#### 5. Permission Denied Errors
**Solution**: Use user installation
```bash
pip install --user -r requirements.txt
```

## 📊 Package Categories

### Essential Packages (Required)
- `httpx`, `aiohttp`, `requests` - HTTP clients
- `pandas`, `numpy`, `scipy` - Data analysis
- `python-metar` - METAR parsing
- `siphon` - Model data access
- `metpy` - Weather calculations
- `python-dotenv` - Environment variables
- `pydantic` - Data validation

### Recommended Packages (Highly Useful)
- `noaa-sdk` - Official NOAA SDK
- `pynws` - NWS API client
- `diskcache` - Data caching
- `xarray` - Multi-dimensional arrays
- `netCDF4` - Weather data formats

### Optional Packages (Nice to Have)
- `cartopy` - Weather maps
- `matplotlib` - Visualization
- `redis` - Distributed caching
- `tensorflow`, `torch` - Machine learning
- `tropycal` - Hurricane tracking

## 📝 Environment Variables

Create a `.env` file in the project root:
```bash
# API Keys
KALSHI_API_KEY=your_kalshi_api_key
KALSHI_API_SECRET=your_kalshi_api_secret
XAI_API_KEY=your_xai_api_key
OPENAI_API_KEY=your_openai_api_key

# Environment
KALSHI_ENV=production  # or 'sandbox' for testing
LOG_LEVEL=INFO

# Optional
SENTRY_DSN=your_sentry_dsn
REDIS_URL=redis://localhost:6379
```

## 🎉 Installation Complete!

Once installation is complete, you can:

1. **Test the weather data**:
   ```bash
   python test_weather_data.py
   ```

2. **Configure API keys**:
   ```bash
   python settings_manager.py
   ```

3. **Run the trading bot**:
   ```bash
   python deploy_weather_bot.py --mode test
   ```

## 📚 Additional Resources

- [NOAA API Documentation](https://www.weather.gov/documentation/services-web-api)
- [Kalshi API Documentation](https://trading-api.readme.io/reference/)
- [Python Weather Libraries Guide](https://unidata.github.io/python-training/)
- [Project Documentation](README_WEATHER_BOT.md)

## 💬 Need Help?

If you encounter issues:
1. Check the [Troubleshooting](#-troubleshooting) section
2. Review error messages carefully
3. Ensure all prerequisites are installed
4. Try installing packages individually
5. Check Python version compatibility

---

*Last Updated: August 19, 2025*
*Version: 2.0.0*
