"""
Rate limiting and circuit breaker utilities for API calls.
Prevents excessive API usage and handles failures gracefully.
"""

import asyncio
import time
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Optional, Callable, Any
import structlog

logger = structlog.get_logger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"       # Normal operation
    OPEN = "open"           # Circuit is open (blocking requests)
    HALF_OPEN = "half_open" # Testing if circuit can close


@dataclass
class RateLimitConfig:
    """Rate limiting configuration."""
    max_requests: int
    time_window_seconds: int
    burst_allowance: int = 0  # Extra requests allowed in burst


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration."""
    failure_threshold: int
    timeout_seconds: int
    success_threshold: int = 1  # Successes needed to close circuit


class TokenBucketRateLimiter:
    """Token bucket rate limiter implementation."""
    
    def __init__(self, max_requests: int, time_window_seconds: int, burst_allowance: int = 0):
        self.max_requests = max_requests
        self.time_window = time_window_seconds
        self.burst_allowance = burst_allowance
        self.max_tokens = max_requests + burst_allowance
        self.tokens = self.max_tokens
        self.last_refill = time.time()
        self.lock = asyncio.Lock()
    
    async def acquire(self, tokens_needed: int = 1) -> bool:
        """
        Acquire tokens from the bucket.
        
        Args:
            tokens_needed: Number of tokens needed
            
        Returns:
            True if tokens acquired, False if rate limited
        """
        async with self.lock:
            now = time.time()
            
            # Refill tokens based on time elapsed
            elapsed = now - self.last_refill
            tokens_to_add = int(elapsed * (self.max_requests / self.time_window))
            
            if tokens_to_add > 0:
                self.tokens = min(self.max_tokens, self.tokens + tokens_to_add)
                self.last_refill = now
            
            # Check if we have enough tokens
            if self.tokens >= tokens_needed:
                self.tokens -= tokens_needed
                return True
            else:
                logger.warning(
                    "Rate limit exceeded",
                    tokens_available=self.tokens,
                    tokens_needed=tokens_needed,
                    max_requests=self.max_requests,
                    time_window=self.time_window
                )
                return False
    
    async def get_wait_time(self) -> float:
        """Get estimated wait time until tokens are available."""
        async with self.lock:
            if self.tokens > 0:
                return 0.0
            
            # Calculate when next token will be available
            tokens_per_second = self.max_requests / self.time_window
            wait_time = (1.0 - self.tokens) / tokens_per_second
            return max(0.0, wait_time)


class CircuitBreaker:
    """Circuit breaker for API calls."""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function through circuit breaker.
        
        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenError: If circuit is open
        """
        async with self.lock:
            # Check circuit state
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info("Circuit breaker moving to HALF_OPEN state")
                else:
                    raise CircuitBreakerOpenError("Circuit breaker is OPEN")
            
        # Execute function
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            await self._on_success()
            return result
            
        except Exception as e:
            await self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit should attempt to reset."""
        if self.last_failure_time is None:
            return False
        
        elapsed = time.time() - self.last_failure_time
        return elapsed >= self.config.timeout_seconds
    
    async def _on_success(self):
        """Handle successful call."""
        async with self.lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
                    logger.info("Circuit breaker CLOSED - service recovered")
            elif self.state == CircuitState.CLOSED:
                self.failure_count = max(0, self.failure_count - 1)  # Gradually decrease failure count
    
    async def _on_failure(self):
        """Handle failed call."""
        async with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker OPEN - service still failing")
            elif self.state == CircuitState.CLOSED and self.failure_count >= self.config.failure_threshold:
                self.state = CircuitState.OPEN
                logger.warning(f"Circuit breaker OPEN - {self.failure_count} failures detected")


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open."""
    pass


class APIRateLimiter:
    """Comprehensive rate limiter for different API services."""
    
    def __init__(self):
        self.limiters: Dict[str, TokenBucketRateLimiter] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.call_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Default configurations
        self.default_configs = {
            "kalshi": RateLimitConfig(max_requests=10, time_window_seconds=60, burst_allowance=2),
            "openai": RateLimitConfig(max_requests=20, time_window_seconds=60, burst_allowance=5),
            "xai": RateLimitConfig(max_requests=15, time_window_seconds=60, burst_allowance=3),
            "noaa": RateLimitConfig(max_requests=30, time_window_seconds=60, burst_allowance=10),
        }
        
        # Initialize rate limiters
        for service, config in self.default_configs.items():
            self.limiters[service] = TokenBucketRateLimiter(
                config.max_requests, 
                config.time_window_seconds, 
                config.burst_allowance
            )
            
            # Initialize circuit breakers
            self.circuit_breakers[service] = CircuitBreaker(
                CircuitBreakerConfig(
                    failure_threshold=5,
                    timeout_seconds=60,
                    success_threshold=2
                )
            )
    
    async def acquire(self, service: str, tokens: int = 1) -> bool:
        """
        Acquire rate limit tokens for a service.
        
        Args:
            service: Service name (e.g., 'kalshi', 'openai')
            tokens: Number of tokens to acquire
            
        Returns:
            True if acquired, False if rate limited
        """
        if service not in self.limiters:
            logger.warning(f"No rate limiter configured for service: {service}")
            return True
        
        result = await self.limiters[service].acquire(tokens)
        
        if result:
            self.call_history[service].append(time.time())
            logger.debug(f"Rate limit token acquired for {service}")
        else:
            logger.warning(f"Rate limit exceeded for {service}")
        
        return result
    
    async def call_with_circuit_breaker(self, service: str, func: Callable, *args, **kwargs) -> Any:
        """
        Call function with circuit breaker protection.
        
        Args:
            service: Service name
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
        """
        if service not in self.circuit_breakers:
            logger.warning(f"No circuit breaker configured for service: {service}")
            return await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
        
        return await self.circuit_breakers[service].call(func, *args, **kwargs)
    
    async def get_wait_time(self, service: str) -> float:
        """Get estimated wait time for service."""
        if service not in self.limiters:
            return 0.0
        
        return await self.limiters[service].get_wait_time()
    
    def get_service_stats(self, service: str) -> Dict[str, Any]:
        """Get statistics for a service."""
        stats = {
            "service": service,
            "rate_limiter_configured": service in self.limiters,
            "circuit_breaker_configured": service in self.circuit_breakers,
            "total_calls": len(self.call_history.get(service, [])),
        }
        
        if service in self.limiters:
            limiter = self.limiters[service]
            stats.update({
                "available_tokens": limiter.tokens,
                "max_tokens": limiter.max_tokens,
                "time_window": limiter.time_window,
            })
        
        if service in self.circuit_breakers:
            breaker = self.circuit_breakers[service]
            stats.update({
                "circuit_state": breaker.state.value,
                "failure_count": breaker.failure_count,
                "success_count": breaker.success_count,
            })
        
        # Recent call statistics
        recent_calls = [
            call_time for call_time in self.call_history.get(service, [])
            if time.time() - call_time < 300  # Last 5 minutes
        ]
        stats["recent_calls_5min"] = len(recent_calls)
        
        return stats
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all services."""
        return {service: self.get_service_stats(service) for service in self.default_configs.keys()}


# Global rate limiter instance
global_rate_limiter = APIRateLimiter()


# Decorator for rate limiting
def rate_limited(service: str, tokens: int = 1):
    """Decorator to add rate limiting to functions."""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                if await global_rate_limiter.acquire(service, tokens):
                    return await func(*args, **kwargs)
                else:
                    wait_time = await global_rate_limiter.get_wait_time(service)
                    raise Exception(f"Rate limited for {service}. Wait {wait_time:.2f}s")
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                # For sync functions, we can't use async acquire
                logger.warning("Rate limiting decorator used on sync function - limited functionality")
                return func(*args, **kwargs)
            return sync_wrapper
    return decorator


# Decorator for circuit breaker
def circuit_breaker_protected(service: str):
    """Decorator to add circuit breaker protection to functions."""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                return await global_rate_limiter.call_with_circuit_breaker(service, func, *args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                logger.warning("Circuit breaker decorator used on sync function - limited functionality")
                return func(*args, **kwargs)
            return sync_wrapper
    return decorator