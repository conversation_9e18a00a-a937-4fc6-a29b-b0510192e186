"""
Weather Dashboard Extension for Beast Mode Dashboard

This module extends the existing dashboard with weather-specific panels
for monitoring temperature data, forecasts, and trading opportunities.
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import pandas as pd

from src.weather.noaa_client import NOAAClient
from src.weather.weather_analyzer import WeatherAnalyzer
from src.clients.xai_client import XAIClient
from src.utils.logging_setup import get_trading_logger


class WeatherDashboardExtension:
    """
    Weather-specific dashboard components.
    
    Integrates with the existing beast_mode_dashboard.py to provide
    real-time weather monitoring and trading insights.
    """
    
    def __init__(self):
        self.noaa_client = NOAAClient()
        self.weather_analyzer = None  # Will be initialized with XAI client
        self.logger = get_trading_logger("weather_dashboard")
        
    async def initialize(self, xai_client: XAIClient):
        """Initialize weather analyzer with XAI client."""
        self.weather_analyzer = WeatherAnalyzer(xai_client, self.noaa_client)
        
    async def display_weather_overview(self):
        """Display comprehensive weather overview panel."""
        print("\n🌡️ WEATHER MARKETS OVERVIEW")
        print("-" * 60)
        
        try:
            # Get current observations
            observations = await self.noaa_client.get_all_station_observations()
            
            if not observations:
                print("⚠️ No weather data available")
                return
            
            # Display header
            print(f"{'Station':<10} {'Market':<10} {'Current':<10} {'24hr Max':<10} {'Conditions':<20}")
            print("-" * 60)
            
            # Station to market mapping
            market_map = {
                'KNYC': 'NHIGH',
                'KMIA': 'MIAHIGH',
                'KMDW': 'CHIHIGH',
                'KDEN': 'DENHIGH',
                'KAUS': 'AUSHIGH',
                'KLAX': 'LAHIGH',
                'KPHL': 'PHILHIGH'
            }
            
            for station_id, obs in observations.items():
                market = market_map.get(station_id, 'UNKNOWN')
                current = f"{obs.temperature_f:.1f}°F"
                max_24hr = f"{obs.twenty_four_hour_max:.1f}°F" if obs.twenty_four_hour_max else "N/A"
                conditions = obs.sky_conditions[:20] if len(obs.sky_conditions) > 20 else obs.sky_conditions
                
                print(f"{station_id:<10} {market:<10} {current:<10} {max_24hr:<10} {conditions:<20}")
                
        except Exception as e:
            self.logger.error(f"Error displaying weather overview: {e}")
            print(f"❌ Error: {e}")
    
    async def display_temperature_trajectories(self):
        """Display temperature trajectory analysis panel."""
        print("\n📈 TEMPERATURE TRAJECTORIES")
        print("-" * 60)
        
        try:
            observations = await self.noaa_client.get_all_station_observations()
            
            if not observations:
                print("⚠️ No trajectory data available")
                return
            
            stations_info = {
                'KNYC': {'name': 'New York', 'strike': 85},
                'KMIA': {'name': 'Miami', 'strike': 90},
                'KMDW': {'name': 'Chicago', 'strike': 85},
                'KDEN': {'name': 'Denver', 'strike': 90},
                'KAUS': {'name': 'Austin', 'strike': 100},
                'KLAX': {'name': 'Los Angeles', 'strike': 80},
                'KPHL': {'name': 'Philadelphia', 'strike': 85}
            }
            
            for station_id, obs in observations.items():
                if station_id not in stations_info:
                    continue
                    
                info = stations_info[station_id]
                current_temp = obs.temperature_f
                strike = info['strike']
                
                # Simple trajectory calculation
                current_hour = datetime.now().hour
                hours_to_peak = max(0, 15 - current_hour)  # Peak at 3 PM
                
                # Rate based on conditions
                if 'CLR' in obs.sky_conditions or 'FEW' in obs.sky_conditions:
                    rate = 3.0  # Clear = stronger heating
                else:
                    rate = 1.5  # Cloudy = limited heating
                
                expected_high = current_temp + (rate * hours_to_peak)
                
                # Status emoji
                if expected_high >= strike:
                    status = "🔥 LIKELY"
                elif expected_high >= strike - 3:
                    status = "⚠️ POSSIBLE"
                else:
                    status = "❄️ UNLIKELY"
                
                print(f"{info['name']:<15} Current: {current_temp:.1f}°F → Expected: {expected_high:.1f}°F (Strike: {strike}°F) {status}")
                
        except Exception as e:
            self.logger.error(f"Error displaying trajectories: {e}")
            print(f"❌ Error: {e}")
    
    async def display_model_consensus(self):
        """Display weather model consensus panel."""
        print("\n🎯 MODEL CONSENSUS")
        print("-" * 60)
        
        try:
            # Get consensus for NYC as example
            nyc_station = self.noaa_client.WEATHER_STATIONS['KNYC']
            consensus = await self.noaa_client.get_model_consensus(nyc_station, hour=12)
            
            if consensus:
                print("12-Hour Forecast Consensus (NYC):")
                print(f"{'Model':<10} {'Temperature':<15} {'Deviation':<15}")
                print("-" * 40)
                
                avg_temp = sum(consensus.values()) / len(consensus)
                
                for model, temp in consensus.items():
                    deviation = temp - avg_temp
                    dev_str = f"{deviation:+.1f}°F"
                    print(f"{model:<10} {temp:.1f}°F {dev_str:<15}")
                
                print("-" * 40)
                print(f"{'Average':<10} {avg_temp:.1f}°F")
                print(f"{'Spread':<10} {max(consensus.values()) - min(consensus.values()):.1f}°F")
            else:
                print("⚠️ No model consensus available")
                
        except Exception as e:
            self.logger.error(f"Error displaying model consensus: {e}")
            print(f"❌ Error: {e}")
    
    async def display_trading_opportunities(self):
        """Display weather trading opportunities panel."""
        print("\n💰 WEATHER TRADING OPPORTUNITIES")
        print("-" * 60)
        
        try:
            if not self.weather_analyzer:
                print("⚠️ Weather analyzer not initialized")
                return
            
            # Mock market prices for demonstration
            mock_prices = {
                'KNYC': 0.50,
                'KMIA': 0.65,
                'KMDW': 0.45,
                'KDEN': 0.40,
                'KAUS': 0.70,
                'KLAX': 0.55,
                'KPHL': 0.48
            }
            
            analyses = await self.weather_analyzer.analyze_all_markets(mock_prices)
            
            if not analyses:
                print("⚠️ No opportunities found")
                return
            
            # Sort by edge
            analyses.sort(key=lambda x: abs(x.edge), reverse=True)
            
            print(f"{'Market':<10} {'Edge':<10} {'Confidence':<12} {'Signal':<10} {'Risk':<10}")
            print("-" * 60)
            
            for analysis in analyses[:5]:  # Top 5 opportunities
                edge_str = f"{analysis.edge:+.3f}"
                conf_str = f"{analysis.confidence_score:.0f}%"
                
                # Signal emoji
                if analysis.entry_recommendation == 'BUY':
                    signal = "📈 BUY"
                elif analysis.entry_recommendation == 'SELL':
                    signal = "📉 SELL"
                else:
                    signal = "⏸️ HOLD"
                
                print(f"{analysis.market_ticker:<10} {edge_str:<10} {conf_str:<12} {signal:<10} {analysis.settlement_risk:<10}")
            
        except Exception as e:
            self.logger.error(f"Error displaying opportunities: {e}")
            print(f"❌ Error: {e}")
    
    async def display_weather_alerts(self):
        """Display weather alerts and warnings panel."""
        print("\n⚠️ WEATHER ALERTS")
        print("-" * 60)
        
        alerts = []
        
        try:
            observations = await self.noaa_client.get_all_station_observations()
            
            for station_id, obs in observations.items():
                # Check for extreme conditions
                if obs.temperature_f > 100:
                    alerts.append(f"🔥 {station_id}: Extreme heat ({obs.temperature_f:.1f}°F)")
                elif obs.temperature_f < 32:
                    alerts.append(f"❄️ {station_id}: Freezing conditions ({obs.temperature_f:.1f}°F)")
                
                if obs.wind_speed > 25:
                    alerts.append(f"💨 {station_id}: High winds ({obs.wind_speed:.0f} kt)")
                
                if 'TS' in obs.sky_conditions or 'CB' in obs.sky_conditions:
                    alerts.append(f"⛈️ {station_id}: Thunderstorms detected")
            
            if alerts:
                for alert in alerts:
                    print(f"  • {alert}")
            else:
                print("  ✅ No weather alerts at this time")
                
        except Exception as e:
            self.logger.error(f"Error displaying alerts: {e}")
            print(f"  ❌ Error retrieving alerts: {e}")
    
    async def get_weather_metrics(self) -> Dict:
        """Get weather metrics for integration with main dashboard."""
        try:
            observations = await self.noaa_client.get_all_station_observations()
            
            if not observations:
                return {}
            
            temps = [obs.temperature_f for obs in observations.values() if obs.temperature_f]
            
            return {
                'stations_reporting': len(observations),
                'avg_temperature': sum(temps) / len(temps) if temps else 0,
                'max_temperature': max(temps) if temps else 0,
                'min_temperature': min(temps) if temps else 0,
                'last_update': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting weather metrics: {e}")
            return {}
    
    async def display_full_weather_dashboard(self):
        """Display complete weather dashboard."""
        print("\n" + "=" * 60)
        print("🌡️ WEATHER TRADING DASHBOARD")
        print("=" * 60)
        
        # Display all panels
        await self.display_weather_overview()
        await self.display_temperature_trajectories()
        await self.display_model_consensus()
        await self.display_trading_opportunities()
        await self.display_weather_alerts()
        
        # Footer
        print("\n" + "=" * 60)
        print(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("Updates every 15 minutes | Data: NOAA/NWS")


async def test_weather_dashboard():
    """Test weather dashboard functionality."""
    
    print("Testing Weather Dashboard Extension...")
    
    # Create mock XAI client
    class MockXAIClient:
        async def analyze_market(self, market_data, historical_data):
            return {
                'content': 'Test analysis',
                'confidence': 75
            }
    
    dashboard = WeatherDashboardExtension()
    await dashboard.initialize(MockXAIClient())
    
    # Display full dashboard
    await dashboard.display_full_weather_dashboard()
    
    # Get metrics for integration
    metrics = await dashboard.get_weather_metrics()
    print(f"\n📊 Integration Metrics: {metrics}")
    
    print("\n✅ Weather Dashboard test complete!")


# Integration function for beast_mode_dashboard.py
async def integrate_weather_panels(dashboard_instance):
    """
    Integration function to add weather panels to existing dashboard.
    
    This function should be called from beast_mode_dashboard.py to add
    weather-specific panels to the main dashboard.
    """
    weather_ext = WeatherDashboardExtension()
    
    # Initialize with the dashboard's XAI client
    if hasattr(dashboard_instance, 'xai_client'):
        await weather_ext.initialize(dashboard_instance.xai_client)
    
    # Display weather panels
    await weather_ext.display_weather_overview()
    await weather_ext.display_temperature_trajectories()
    await weather_ext.display_trading_opportunities()
    
    return weather_ext


if __name__ == "__main__":
    asyncio.run(test_weather_dashboard())
