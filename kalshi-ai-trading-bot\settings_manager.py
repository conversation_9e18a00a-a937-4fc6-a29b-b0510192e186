#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Secure Settings Manager
This provides a GUI for safely entering and storing API keys.
"""

import os
import json
import base64
import getpass
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from pathlib import Path
import sys

class SecureSettingsManager:
    """Secure settings manager for API keys and configuration."""
    
    def __init__(self):
        self.config_dir = Path.home() / '.weather_trading_bot'
        self.config_file = self.config_dir / 'config.enc'
        self.key_file = self.config_dir / '.key'
        
        # Create config directory if it doesn't exist
        self.config_dir.mkdir(exist_ok=True)
        
        # Initialize encryption
        self.cipher = self._get_or_create_cipher()
        
    def _get_or_create_cipher(self):
        """Get existing cipher or create new one."""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                key = f.read()
        else:
            # Generate a new key
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # Set restrictive permissions (Unix-like systems)
            if os.name != 'nt':
                os.chmod(self.key_file, 0o600)
        
        return Fernet(key)
    
    def save_settings(self, settings):
        """Encrypt and save settings."""
        json_str = json.dumps(settings)
        encrypted = self.cipher.encrypt(json_str.encode())
        
        with open(self.config_file, 'wb') as f:
            f.write(encrypted)
        
        # Set restrictive permissions
        if os.name != 'nt':
            os.chmod(self.config_file, 0o600)
    
    def load_settings(self):
        """Load and decrypt settings."""
        if not self.config_file.exists():
            return {}
        
        try:
            with open(self.config_file, 'rb') as f:
                encrypted = f.read()
            
            decrypted = self.cipher.decrypt(encrypted)
            return json.loads(decrypted.decode())
        except Exception as e:
            print(f"Error loading settings: {e}")
            return {}
    
    def export_to_env(self, settings):
        """Export settings as environment variables."""
        for key, value in settings.items():
            if value:
                os.environ[key] = value

class SettingsGUI:
    """GUI for managing bot settings."""
    
    def __init__(self):
        self.manager = SecureSettingsManager()
        self.settings = self.manager.load_settings()
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("Kalshi AI Trading Bot - Settings Manager")
        self.root.geometry("700x850")
        
        # Set icon if available
        try:
            self.root.iconbitmap(default='weather_bot.ico')
        except:
            pass
        
        # Apply modern theme
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Custom colors
        self.bg_color = "#f0f0f0"
        self.fg_color = "#333333"
        self.accent_color = "#4CAF50"
        self.danger_color = "#f44336"
        
        self.root.configure(bg=self.bg_color)
        
        self.create_widgets()
        self.load_current_settings()
        
    def create_widgets(self):
        """Create all GUI widgets."""
        
        # Header
        header_frame = tk.Frame(self.root, bg=self.accent_color, height=80)
        header_frame.pack(fill=tk.X, padx=0, pady=0)
        
        title_label = tk.Label(
            header_frame,
            text="🌡️ Weather Trading Bot Settings",
            font=("Arial", 20, "bold"),
            bg=self.accent_color,
            fg="white"
        )
        title_label.pack(pady=20)
        
        # Main container
        main_frame = tk.Frame(self.root, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # API Keys Tab
        self.api_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.api_frame, text="API Keys")
        self.create_api_tab()
        
        # Trading Settings Tab
        self.trading_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.trading_frame, text="Trading Settings")
        self.create_trading_tab()
        
        # Advanced Tab
        self.advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.advanced_frame, text="Advanced")
        self.create_advanced_tab()
        
        # Status Tab
        self.status_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.status_frame, text="Status")
        self.create_status_tab()
        
        # Bottom buttons
        button_frame = tk.Frame(self.root, bg=self.bg_color)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Save button
        save_btn = tk.Button(
            button_frame,
            text="💾 Save Settings",
            command=self.save_settings,
            bg=self.accent_color,
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # Test Connection button
        test_btn = tk.Button(
            button_frame,
            text="🔌 Test Connections",
            command=self.test_connections,
            bg="#2196F3",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        test_btn.pack(side=tk.LEFT, padx=5)
        
        # Export .env button
        export_btn = tk.Button(
            button_frame,
            text="📄 Export .env File",
            command=self.export_env_file,
            bg="#FF9800",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        export_btn.pack(side=tk.LEFT, padx=5)
        
        # Open Dashboard button
        dashboard_btn = tk.Button(
            button_frame,
            text="📊 Open Trading Dashboard",
            command=self.open_trading_dashboard,
            bg="#9C27B0",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        dashboard_btn.pack(side=tk.LEFT, padx=5)
        
        # Exit button
        exit_btn = tk.Button(
            button_frame,
            text="❌ Exit",
            command=self.root.quit,
            bg=self.danger_color,
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2"
        )
        exit_btn.pack(side=tk.RIGHT, padx=5)
        
    def create_api_tab(self):
        """Create API keys configuration tab."""
        # Create scrollable frame
        canvas = tk.Canvas(self.api_frame, bg="white")
        scrollbar = ttk.Scrollbar(self.api_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Kalshi API Section
        kalshi_label = tk.Label(
            scrollable_frame,
            text="Kalshi API Configuration",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        kalshi_label.grid(row=0, column=0, columnspan=2, pady=10, sticky="w")
        
        # Kalshi API Key
        tk.Label(scrollable_frame, text="API Key:", font=("Arial", 10)).grid(
            row=1, column=0, sticky="e", padx=10, pady=5
        )
        self.kalshi_key_entry = tk.Entry(scrollable_frame, width=50, show="*")
        self.kalshi_key_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # Show/Hide button for Kalshi key
        self.kalshi_show_btn = tk.Button(
            scrollable_frame,
            text="👁",
            command=lambda: self.toggle_visibility(self.kalshi_key_entry, self.kalshi_show_btn),
            width=3
        )
        self.kalshi_show_btn.grid(row=1, column=2, padx=5)
        
        # Kalshi API Secret
        tk.Label(scrollable_frame, text="API Secret:", font=("Arial", 10)).grid(
            row=2, column=0, sticky="e", padx=10, pady=5
        )
        self.kalshi_secret_entry = tk.Entry(scrollable_frame, width=50, show="*")
        self.kalshi_secret_entry.grid(row=2, column=1, padx=10, pady=5)
        
        # Show/Hide button for Kalshi secret
        self.kalshi_secret_show_btn = tk.Button(
            scrollable_frame,
            text="👁",
            command=lambda: self.toggle_visibility(self.kalshi_secret_entry, self.kalshi_secret_show_btn),
            width=3
        )
        self.kalshi_secret_show_btn.grid(row=2, column=2, padx=5)
        
        # Kalshi Environment
        tk.Label(scrollable_frame, text="Environment:", font=("Arial", 10)).grid(
            row=3, column=0, sticky="e", padx=10, pady=5
        )
        self.kalshi_env_var = tk.StringVar(value="demo")
        env_menu = ttk.Combobox(
            scrollable_frame,
            textvariable=self.kalshi_env_var,
            values=["demo", "production"],
            state="readonly",
            width=47
        )
        env_menu.grid(row=3, column=1, padx=10, pady=5)
        
        # Separator
        ttk.Separator(scrollable_frame, orient="horizontal").grid(
            row=4, column=0, columnspan=3, sticky="ew", pady=20
        )
        
        # LLM API Section
        llm_label = tk.Label(
            scrollable_frame,
            text="LLM/AI Configuration",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        llm_label.grid(row=5, column=0, columnspan=2, pady=10, sticky="w")
        
        # LLM Provider
        tk.Label(scrollable_frame, text="AI Provider:", font=("Arial", 10)).grid(
            row=6, column=0, sticky="e", padx=10, pady=5
        )
        self.llm_provider_var = tk.StringVar(value="xai")
        self.provider_menu = ttk.Combobox(
            scrollable_frame,
            textvariable=self.llm_provider_var,
            values=["xai", "openai", "openrouter"],
            state="readonly",
            width=47
        )
        self.provider_menu.grid(row=6, column=1, padx=10, pady=5)

        # Bind provider change event
        self.provider_menu.bind("<<ComboboxSelected>>", self.on_provider_change)

        # Add helpful note about xAI
        self.provider_note_label = tk.Label(
            scrollable_frame,
            text="💡 Recommended: xAI (Grok-4) - optimized for trading decisions",
            font=("Arial", 9),
            fg="gray"
        )
        self.provider_note_label.grid(row=6, column=2, padx=10, pady=5, sticky="w")

        # Store references to all provider-specific widgets for show/hide
        self.provider_widgets = {}

        # XAI API Key Section
        self.provider_widgets['xai'] = []

        self.xai_key_label = tk.Label(scrollable_frame, text="XAI API Key:", font=("Arial", 10))
        self.xai_key_label.grid(row=7, column=0, sticky="e", padx=10, pady=5)
        self.provider_widgets['xai'].append(self.xai_key_label)

        # Add link to get xAI key
        self.xai_link_label = tk.Label(
            scrollable_frame,
            text="(Get key at: x.ai/api)",
            font=("Arial", 9),
            fg="blue",
            cursor="hand2"
        )
        self.xai_link_label.grid(row=8, column=0, sticky="e", padx=10, pady=2)
        self.xai_link_label.bind("<Button-1>", lambda e: self.open_url("https://console.x.ai/"))
        self.provider_widgets['xai'].append(self.xai_link_label)

        self.xai_key_entry = tk.Entry(scrollable_frame, width=50, show="*")
        self.xai_key_entry.grid(row=7, column=1, padx=10, pady=5)
        self.provider_widgets['xai'].append(self.xai_key_entry)

        # Show/Hide button for XAI key
        self.xai_show_btn = tk.Button(
            scrollable_frame,
            text="👁",
            command=lambda: self.toggle_visibility(self.xai_key_entry, self.xai_show_btn),
            width=3
        )
        self.xai_show_btn.grid(row=7, column=2, padx=5)
        self.provider_widgets['xai'].append(self.xai_show_btn)

        # OpenAI API Key Section
        self.provider_widgets['openai'] = []

        self.openai_key_label = tk.Label(scrollable_frame, text="OpenAI API Key:", font=("Arial", 10))
        self.openai_key_label.grid(row=9, column=0, sticky="e", padx=10, pady=5)
        self.provider_widgets['openai'].append(self.openai_key_label)

        self.openai_link_label = tk.Label(
            scrollable_frame,
            text="(Get key at: platform.openai.com)",
            font=("Arial", 9),
            fg="blue",
            cursor="hand2"
        )
        self.openai_link_label.grid(row=10, column=0, sticky="e", padx=10, pady=2)
        self.openai_link_label.bind("<Button-1>", lambda e: self.open_url("https://platform.openai.com/api-keys"))
        self.provider_widgets['openai'].append(self.openai_link_label)

        self.openai_key_entry = tk.Entry(scrollable_frame, width=50, show="*")
        self.openai_key_entry.grid(row=9, column=1, padx=10, pady=5)
        self.provider_widgets['openai'].append(self.openai_key_entry)

        # Show/Hide button for OpenAI key
        self.openai_show_btn = tk.Button(
            scrollable_frame,
            text="👁",
            command=lambda: self.toggle_visibility(self.openai_key_entry, self.openai_show_btn),
            width=3
        )
        self.openai_show_btn.grid(row=9, column=2, padx=5)
        self.provider_widgets['openai'].append(self.openai_show_btn)

        # OpenRouter API Key Section (for future use)
        self.provider_widgets['openrouter'] = []

        self.openrouter_key_label = tk.Label(scrollable_frame, text="OpenRouter API Key:", font=("Arial", 10))
        self.openrouter_key_label.grid(row=11, column=0, sticky="e", padx=10, pady=5)
        self.provider_widgets['openrouter'].append(self.openrouter_key_label)

        self.openrouter_link_label = tk.Label(
            scrollable_frame,
            text="(Get key at: openrouter.ai)",
            font=("Arial", 9),
            fg="blue",
            cursor="hand2"
        )
        self.openrouter_link_label.grid(row=12, column=0, sticky="e", padx=10, pady=2)
        self.openrouter_link_label.bind("<Button-1>", lambda e: self.open_url("https://openrouter.ai/keys"))
        self.provider_widgets['openrouter'].append(self.openrouter_link_label)

        self.openrouter_key_entry = tk.Entry(scrollable_frame, width=50, show="*")
        self.openrouter_key_entry.grid(row=11, column=1, padx=10, pady=5)
        self.provider_widgets['openrouter'].append(self.openrouter_key_entry)

        # Show/Hide button for OpenRouter key
        self.openrouter_show_btn = tk.Button(
            scrollable_frame,
            text="👁",
            command=lambda: self.toggle_visibility(self.openrouter_key_entry, self.openrouter_show_btn),
            width=3
        )
        self.openrouter_show_btn.grid(row=11, column=2, padx=5)
        self.provider_widgets['openrouter'].append(self.openrouter_show_btn)

        # Model selection for OpenRouter (future feature)
        self.openrouter_model_label = tk.Label(scrollable_frame, text="Model:", font=("Arial", 10))
        self.openrouter_model_label.grid(row=13, column=0, sticky="e", padx=10, pady=5)
        self.provider_widgets['openrouter'].append(self.openrouter_model_label)

        self.openrouter_model_var = tk.StringVar(value="anthropic/claude-3.5-sonnet")
        self.openrouter_model_menu = ttk.Combobox(
            scrollable_frame,
            textvariable=self.openrouter_model_var,
            values=[
                "anthropic/claude-3.5-sonnet",
                "openai/gpt-4-turbo",
                "google/gemini-pro",
                "meta-llama/llama-3.1-405b"
            ],
            state="readonly",
            width=47
        )
        self.openrouter_model_menu.grid(row=13, column=1, padx=10, pady=5)
        self.provider_widgets['openrouter'].append(self.openrouter_model_menu)

        # Initialize visibility based on current selection
        self.on_provider_change(None)
        
        # Info text
        info_text = tk.Label(
            scrollable_frame,
            text="🔒 All API keys are encrypted and stored securely in your home directory",
            font=("Arial", 9, "italic"),
            fg="#666"
        )
        info_text.grid(row=14, column=0, columnspan=3, pady=20)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_trading_tab(self):
        """Create trading configuration tab."""
        frame = ttk.Frame(self.trading_frame, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Risk Management Section
        risk_label = tk.Label(
            frame,
            text="Risk Management",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        risk_label.grid(row=0, column=0, columnspan=2, pady=10, sticky="w")
        
        # Max Position Size
        tk.Label(frame, text="Max Position Size ($):", font=("Arial", 10)).grid(
            row=1, column=0, sticky="e", padx=10, pady=5
        )
        self.max_position_entry = tk.Entry(frame, width=30)
        self.max_position_entry.insert(0, "25000")
        self.max_position_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # Daily Loss Limit
        tk.Label(frame, text="Daily Loss Limit (%):", font=("Arial", 10)).grid(
            row=2, column=0, sticky="e", padx=10, pady=5
        )
        self.daily_loss_entry = tk.Entry(frame, width=30)
        self.daily_loss_entry.insert(0, "5")
        self.daily_loss_entry.grid(row=2, column=1, padx=10, pady=5)
        
        # Weather Exposure Limit
        tk.Label(frame, text="Weather Exposure (%):", font=("Arial", 10)).grid(
            row=3, column=0, sticky="e", padx=10, pady=5
        )
        self.weather_exposure_entry = tk.Entry(frame, width=30)
        self.weather_exposure_entry.insert(0, "40")
        self.weather_exposure_entry.grid(row=3, column=1, padx=10, pady=5)
        
        # Separator
        ttk.Separator(frame, orient="horizontal").grid(
            row=4, column=0, columnspan=2, sticky="ew", pady=20
        )
        
        # Trading Parameters Section
        params_label = tk.Label(
            frame,
            text="Trading Parameters",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        params_label.grid(row=5, column=0, columnspan=2, pady=10, sticky="w")
        
        # Minimum Edge
        tk.Label(frame, text="Minimum Edge (%):", font=("Arial", 10)).grid(
            row=6, column=0, sticky="e", padx=10, pady=5
        )
        self.min_edge_entry = tk.Entry(frame, width=30)
        self.min_edge_entry.insert(0, "8")
        self.min_edge_entry.grid(row=6, column=1, padx=10, pady=5)
        
        # Update Frequency
        tk.Label(frame, text="Update Frequency (min):", font=("Arial", 10)).grid(
            row=7, column=0, sticky="e", padx=10, pady=5
        )
        self.update_freq_entry = tk.Entry(frame, width=30)
        self.update_freq_entry.insert(0, "15")
        self.update_freq_entry.grid(row=7, column=1, padx=10, pady=5)
        
        # Trading Mode
        tk.Label(frame, text="Default Mode:", font=("Arial", 10)).grid(
            row=8, column=0, sticky="e", padx=10, pady=5
        )
        self.trading_mode_var = tk.StringVar(value="paper")
        mode_menu = ttk.Combobox(
            frame,
            textvariable=self.trading_mode_var,
            values=["paper", "limited", "production"],
            state="readonly",
            width=27
        )
        mode_menu.grid(row=8, column=1, padx=10, pady=5)
        
    def create_advanced_tab(self):
        """Create advanced configuration tab."""
        frame = ttk.Frame(self.advanced_frame, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Data Sources Section
        data_label = tk.Label(
            frame,
            text="Data Sources",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        data_label.grid(row=0, column=0, columnspan=2, pady=10, sticky="w")
        
        # Enable AFD
        self.enable_afd_var = tk.BooleanVar(value=True)
        afd_check = tk.Checkbutton(
            frame,
            text="Enable AFD Analysis",
            variable=self.enable_afd_var,
            font=("Arial", 10)
        )
        afd_check.grid(row=1, column=0, columnspan=2, sticky="w", padx=10, pady=5)
        
        # Enable Model Data
        self.enable_models_var = tk.BooleanVar(value=True)
        models_check = tk.Checkbutton(
            frame,
            text="Enable Model Consensus (HRRR, NAM, GFS)",
            variable=self.enable_models_var,
            font=("Arial", 10)
        )
        models_check.grid(row=2, column=0, columnspan=2, sticky="w", padx=10, pady=5)
        
        # Enable Enhanced Analytics
        self.enable_enhanced_var = tk.BooleanVar(value=True)
        enhanced_check = tk.Checkbutton(
            frame,
            text="Enable Enhanced Analytics (Monte Carlo)",
            variable=self.enable_enhanced_var,
            font=("Arial", 10)
        )
        enhanced_check.grid(row=3, column=0, columnspan=2, sticky="w", padx=10, pady=5)
        
        # Separator
        ttk.Separator(frame, orient="horizontal").grid(
            row=4, column=0, columnspan=2, sticky="ew", pady=20
        )
        
        # Logging Section
        log_label = tk.Label(
            frame,
            text="Logging & Monitoring",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        log_label.grid(row=5, column=0, columnspan=2, pady=10, sticky="w")
        
        # Log Level
        tk.Label(frame, text="Log Level:", font=("Arial", 10)).grid(
            row=6, column=0, sticky="e", padx=10, pady=5
        )
        self.log_level_var = tk.StringVar(value="INFO")
        log_menu = ttk.Combobox(
            frame,
            textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly",
            width=27
        )
        log_menu.grid(row=6, column=1, padx=10, pady=5)
        
        # Dashboard Port
        tk.Label(frame, text="Dashboard Port:", font=("Arial", 10)).grid(
            row=7, column=0, sticky="e", padx=10, pady=5
        )
        self.dashboard_port_entry = tk.Entry(frame, width=30)
        self.dashboard_port_entry.insert(0, "8080")
        self.dashboard_port_entry.grid(row=7, column=1, padx=10, pady=5)
        
        # Enable Alerts
        self.enable_alerts_var = tk.BooleanVar(value=True)
        alerts_check = tk.Checkbutton(
            frame,
            text="Enable Email Alerts",
            variable=self.enable_alerts_var,
            font=("Arial", 10)
        )
        alerts_check.grid(row=8, column=0, columnspan=2, sticky="w", padx=10, pady=5)
        
        # Alert Email
        tk.Label(frame, text="Alert Email:", font=("Arial", 10)).grid(
            row=9, column=0, sticky="e", padx=10, pady=5
        )
        self.alert_email_entry = tk.Entry(frame, width=30)
        self.alert_email_entry.grid(row=9, column=1, padx=10, pady=5)
        
    def create_status_tab(self):
        """Create status and testing tab."""
        frame = ttk.Frame(self.status_frame, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Status Display
        status_label = tk.Label(
            frame,
            text="Connection Status",
            font=("Arial", 14, "bold"),
            fg=self.fg_color
        )
        status_label.pack(pady=10)
        
        # Status Text Area
        self.status_text = scrolledtext.ScrolledText(
            frame,
            width=60,
            height=20,
            font=("Courier", 10),
            bg="#f5f5f5"
        )
        self.status_text.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        # Add initial status message
        self.status_text.insert(tk.END, "Weather Trading Bot Status Monitor\n")
        self.status_text.insert(tk.END, "="*50 + "\n\n")
        self.status_text.insert(tk.END, "Click 'Test Connections' to verify API connectivity\n")
        
    def toggle_visibility(self, entry, button):
        """Toggle password visibility."""
        if entry.cget('show') == '*':
            entry.config(show='')
            button.config(text='🔒')
        else:
            entry.config(show='*')
            button.config(text='👁')
    
    def on_provider_change(self, event):
        """Handle AI provider selection change."""
        selected_provider = self.llm_provider_var.get()

        # Hide all provider-specific widgets first
        for provider, widgets in self.provider_widgets.items():
            for widget in widgets:
                widget.grid_remove()

        # Show widgets for selected provider
        if selected_provider in self.provider_widgets:
            for widget in self.provider_widgets[selected_provider]:
                widget.grid()

        # Update the recommendation note
        provider_notes = {
            "xai": "💡 Recommended: xAI (Grok-4) - optimized for trading decisions",
            "openai": "💡 OpenAI GPT-4 - reliable fallback option",
            "openrouter": "💡 OpenRouter - access to multiple AI models"
        }

        if selected_provider in provider_notes:
            self.provider_note_label.config(text=provider_notes[selected_provider])

    def open_url(self, url):
        """Open URL in default browser."""
        import webbrowser
        webbrowser.open(url)

    def load_current_settings(self):
        """Load current settings into GUI."""
        if 'KALSHI_API_KEY' in self.settings:
            self.kalshi_key_entry.insert(0, self.settings['KALSHI_API_KEY'])
        if 'KALSHI_API_SECRET' in self.settings:
            self.kalshi_secret_entry.insert(0, self.settings['KALSHI_API_SECRET'])
        if 'KALSHI_ENV' in self.settings:
            self.kalshi_env_var.set(self.settings['KALSHI_ENV'])
        if 'LLM_PROVIDER' in self.settings:
            self.llm_provider_var.set(self.settings['LLM_PROVIDER'])
        if 'XAI_API_KEY' in self.settings:
            self.xai_key_entry.insert(0, self.settings['XAI_API_KEY'])
        if 'OPENAI_API_KEY' in self.settings:
            self.openai_key_entry.insert(0, self.settings['OPENAI_API_KEY'])
        if 'OPENROUTER_API_KEY' in self.settings:
            self.openrouter_key_entry.insert(0, self.settings['OPENROUTER_API_KEY'])
        if 'OPENROUTER_MODEL' in self.settings:
            self.openrouter_model_var.set(self.settings['OPENROUTER_MODEL'])

        # Trigger provider change to show correct fields
        self.on_provider_change(None)
        if 'OPENROUTER_API_KEY' in self.settings:
            self.openrouter_key_entry.insert(0, self.settings['OPENROUTER_API_KEY'])
        if 'OPENROUTER_MODEL' in self.settings:
            self.openrouter_model_var.set(self.settings['OPENROUTER_MODEL'])

        # Trigger provider change to show correct fields
        self.on_provider_change(None)
    
    def save_settings(self):
        """Save all settings."""
        self.settings = {
            'KALSHI_API_KEY': self.kalshi_key_entry.get(),
            'KALSHI_API_SECRET': self.kalshi_secret_entry.get(),
            'KALSHI_ENV': self.kalshi_env_var.get(),
            'XAI_API_KEY': self.xai_key_entry.get(),
            'OPENAI_API_KEY': self.openai_key_entry.get(),
            'OPENROUTER_API_KEY': self.openrouter_key_entry.get(),
            'OPENROUTER_MODEL': self.openrouter_model_var.get(),
            'LLM_PROVIDER': self.llm_provider_var.get(),
            'MAX_POSITION_SIZE': self.max_position_entry.get(),
            'DAILY_LOSS_LIMIT': self.daily_loss_entry.get(),
            'WEATHER_EXPOSURE_LIMIT': self.weather_exposure_entry.get(),
            'MIN_EDGE': self.min_edge_entry.get(),
            'UPDATE_FREQUENCY': self.update_freq_entry.get(),
            'TRADING_MODE': self.trading_mode_var.get(),
            'ENABLE_AFD': str(self.enable_afd_var.get()),
            'ENABLE_MODELS': str(self.enable_models_var.get()),
            'ENABLE_ENHANCED': str(self.enable_enhanced_var.get()),
            'LOG_LEVEL': self.log_level_var.get(),
            'DASHBOARD_PORT': self.dashboard_port_entry.get(),
            'ENABLE_ALERTS': str(self.enable_alerts_var.get()),
            'ALERT_EMAIL': self.alert_email_entry.get()
        }
        
        # Save encrypted settings
        self.manager.save_settings(self.settings)
        
        # Export to environment
        self.manager.export_to_env(self.settings)
        
        messagebox.showinfo("Success", "Settings saved successfully!\n\nThe settings are encrypted and stored securely.")
    
    def test_connections(self):
        """Test API connections."""
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, "Testing Connections...\n")
        self.status_text.insert(tk.END, "="*50 + "\n\n")
        
        # Test Kalshi
        self.status_text.insert(tk.END, "🔌 Testing Kalshi API...\n")
        if self.kalshi_key_entry.get() and self.kalshi_secret_entry.get():
            self.status_text.insert(tk.END, "✅ Kalshi credentials provided\n")
            self.status_text.insert(tk.END, f"   Environment: {self.kalshi_env_var.get()}\n")
        else:
            self.status_text.insert(tk.END, "❌ Kalshi credentials missing\n")
        
        self.status_text.insert(tk.END, "\n")
        
        # Test LLM
        self.status_text.insert(tk.END, "🤖 Testing LLM API...\n")
        provider = self.llm_provider_var.get()

        if provider == "xai" and self.xai_key_entry.get():
            self.status_text.insert(tk.END, "✅ xAI (Grok) credentials provided\n")
        elif provider == "openai" and self.openai_key_entry.get():
            self.status_text.insert(tk.END, "✅ OpenAI credentials provided\n")
        elif provider == "openrouter" and self.openrouter_key_entry.get():
            self.status_text.insert(tk.END, "✅ OpenRouter credentials provided\n")
            self.status_text.insert(tk.END, f"   Model: {self.openrouter_model_var.get()}\n")
        else:
            self.status_text.insert(tk.END, f"❌ {provider.upper()} credentials missing\n")

        self.status_text.insert(tk.END, f"   Provider: {provider}\n")
        
        self.status_text.insert(tk.END, "\n")
        
        # Test NOAA (always available)
        self.status_text.insert(tk.END, "🌡️ Testing NOAA Connection...\n")
        self.status_text.insert(tk.END, "✅ NOAA APIs are public (no key required)\n")
        
        self.status_text.insert(tk.END, "\n" + "="*50 + "\n")
        self.status_text.insert(tk.END, "Test complete. Check status above.\n")
        
        # Switch to status tab
        self.notebook.select(self.status_frame)
    
    def export_env_file(self):
        """Export settings to .env file."""
        env_content = []
        env_content.append("# Weather Trading Bot Environment Variables")
        env_content.append("# Generated by Settings Manager")
        env_content.append("")
        
        for key, value in self.settings.items():
            if value and not key.startswith('ENABLE_') and not key.endswith('_VAR'):
                env_content.append(f"{key}={value}")
        
        env_file = Path.cwd() / '.env'
        with open(env_file, 'w') as f:
            f.write('\n'.join(env_content))
        
        messagebox.showinfo(
            "Export Complete",
            f".env file created at:\n{env_file}\n\n"
            "You can now source this file or use it with your deployment."
        )
    
    def open_trading_dashboard(self):
        """Open the Trading Dashboard in browser."""
        import subprocess
        import os
        
        # Launch the trading dashboard using streamlit
        try:
            # Get the path to python executable
            python_exe = sys.executable
            
            # Launch streamlit dashboard in a new process
            subprocess.Popen([
                python_exe, "-m", "streamlit", "run", 
                "trading_dashboard.py",
                "--server.address", "localhost",
                "--server.port", "8501"
            ])
            
            messagebox.showinfo(
                "Trading Dashboard Launched",
                "The Trading Dashboard is starting...\n\n"
                "It will open automatically in your browser at:\n"
                "http://localhost:8501\n\n"
                "If it doesn't open automatically, you can manually navigate to that URL."
            )
            
        except Exception as e:
            messagebox.showerror(
                "Launch Error",
                f"Failed to launch Trading Dashboard:\n{e}\n\n"
                "Please ensure streamlit is installed:\n"
                "pip install streamlit"
            )
    
    def open_dashboard(self):
        """Open the Weather Dashboard in browser."""
        import webbrowser
        port = self.dashboard_port_entry.get() if hasattr(self, 'dashboard_port_entry') else '8080'
        dashboard_url = f"http://localhost:{port}"
        
        # Check if dashboard is running
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', int(port)))
        sock.close()
        
        if result == 0:
            # Dashboard is running, open it
            webbrowser.open(dashboard_url)
            messagebox.showinfo(
                "Dashboard Opened",
                f"Weather Dashboard opened in your browser.\n\nURL: {dashboard_url}"
            )
        else:
            # Dashboard not running, show instructions
            response = messagebox.askyesno(
                "Dashboard Not Running",
                "The Weather Dashboard is not currently running.\n\n"
                "Would you like to start the bot now?\n\n"
                "Click 'Yes' to see instructions for starting the bot."
            )
            if response:
                messagebox.showinfo(
                    "How to Start the Bot",
                    "To start the Weather Trading Bot with Dashboard:\n\n"
                    "1. Save your settings (if not already saved)\n"
                    "2. Open a terminal/command prompt\n"
                    "3. Run: python deploy_weather_bot_secure.py\n\n"
                    "The dashboard will be available at:\n"
                    f"http://localhost:{port}"
                )
    
    def run(self):
        """Run the GUI."""
        # Add info bar at top
        info_frame = tk.Frame(self.root, bg="#E3F2FD", height=40)
        info_frame.pack(fill=tk.X, before=self.root.winfo_children()[0])
        
        info_label = tk.Label(
            info_frame,
            text="🔧 This is the Settings Manager - Configure your API keys and trading parameters here",
            font=("Arial", 10),
            bg="#E3F2FD",
            fg="#1976D2"
        )
        info_label.pack(pady=10)
        
        self.root.mainloop()

def main():
    """Main entry point."""
    # Check if we should run CLI or GUI
    if len(sys.argv) > 1:
        if sys.argv[1] == '--export':
            # Export current settings to environment
            manager = SecureSettingsManager()
            settings = manager.load_settings()
            manager.export_to_env(settings)
            print("Settings exported to environment variables")
            return
        elif sys.argv[1] == '--show':
            # Show current settings (without sensitive data)
            manager = SecureSettingsManager()
            settings = manager.load_settings()
            print("Current Settings:")
            for key, value in settings.items():
                if 'KEY' in key or 'SECRET' in key:
                    print(f"  {key}: ***hidden***")
                else:
                    print(f"  {key}: {value}")
            return
    
    # Run GUI
    app = SettingsGUI()
    app.run()

if __name__ == "__main__":
    main()
