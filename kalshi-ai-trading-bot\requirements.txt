# ==========================================
# Weather Trading Bot v2.0 - Complete Requirements
# ==========================================

# ----- Core HTTP and async libraries -----
httpx==0.27.0
aiohttp==3.9.1
requests==2.31.0
asyncio-throttle==1.0.2  # For rate limiting async requests

# ----- Database -----
aiosqlite==0.19.0
sqlalchemy>=2.0.0  # For advanced database operations

# ----- Crypto libraries for API authentication -----
cryptography==42.0.0
pycryptodome==3.20.0

# ----- AI/LLM Integration -----
openai==1.51.2
anthropic
xai_sdk>=1.0.0  # xAI Grok API client

# ----- Data handling and analysis -----
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.12.0  # For portfolio optimization (Python 3.13 compatible)
scikit-learn>=1.3.0  # For pattern recognition and ML features

# ==========================================
# WEATHER-SPECIFIC PACKAGES
# ==========================================

# ----- NOAA/NWS Data Access -----
python-metar==1.4.0  # METAR observation parsing
siphon==0.9  # NOAA THREDDS data access (models)
metpy==1.6.0  # Meteorological calculations
noaa-sdk==0.1.21  # Official NOAA SDK
pynws==1.5.1  # Pure Python NWS API client

# ----- Weather Data Processing -----
xarray>=2023.0.0  # N-dimensional weather data
netCDF4>=1.6.0  # NetCDF file handling
cfgrib>=0.9.10  # GRIB file reading
h5netcdf>=1.2.0  # HDF5 backend for xarray
zarr>=2.16.0  # Zarr format for weather data

# ----- Additional Weather Tools -----
pydap>=3.4.0  # OPeNDAP client for remote data
cartopy>=0.22.0  # Weather map plotting (optional)
matplotlib>=3.7.0  # Visualization
windrose>=1.9.0  # Wind analysis
pvlib>=0.10.0  # Solar calculations

# ----- Caching and Performance -----
diskcache>=5.6.0  # Disk-based caching for weather data
redis>=5.0.0  # Optional: Redis for distributed caching
cachetools>=5.3.0  # In-memory caching decorators

# ----- Environment variables -----
python-dotenv==1.0.0

# ----- Data validation and security -----
pydantic==2.8.2
pydantic-settings
jsonschema>=4.20.0  # JSON schema validation for security

# ----- Logging and monitoring -----
structlog==23.2.0
loguru>=0.7.0  # Enhanced logging

# ----- Type hints -----
typing-extensions>=4.11

# ----- Date/time handling -----
python-dateutil==2.8.2
pytz==2023.3
pendulum>=3.0.0  # Better timezone handling

# ----- Rate limiting -----
ratelimit==2.2.1
tenacity>=8.2.0  # Advanced retry logic

# ----- Configuration management -----
pyyaml==6.0.1
toml>=0.10.2  # TOML config files
configparser>=6.0.0  # INI config files

# ----- Error tracking and monitoring -----
sentry-sdk==1.39.2

# ==========================================
# GUI AND DASHBOARD
# ==========================================

# ----- GUI Components -----
tkinter  # Usually comes with Python, but listed for clarity
customtkinter>=5.2.0  # Modern tkinter widgets
ttkthemes>=3.2.0  # Tkinter themes

# ----- Web Dashboard -----
flask>=3.0.0  # Web framework for dashboard
flask-cors>=4.0.0  # CORS support
flask-socketio>=5.3.0  # WebSocket support
python-socketio>=5.10.0  # Socket.IO server

# ----- Security -----
bcrypt>=4.1.0  # Password hashing
keyring>=24.0.0  # Secure credential storage

# ==========================================
# KALSHI TRADING
# ==========================================

# ----- Kalshi API (if official package available) -----
# kalshi-python>=1.0.0  # Uncomment when official package is available

# ==========================================
# TESTING AND DEVELOPMENT
# ==========================================

# ----- Testing -----
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov>=4.1.0  # Coverage reports
pytest-mock>=3.12.0  # Mocking support
responses>=0.24.0  # Mock HTTP responses
freezegun>=1.4.0  # Time mocking

# ----- Development tools -----
black==23.12.1  # Code formatting
isort==5.13.2  # Import sorting
flake8>=6.1.0  # Linting
mypy>=1.7.0  # Type checking
pylint>=3.0.0  # Code analysis
pre-commit>=3.5.0  # Git hooks

# ----- Documentation -----
sphinx>=7.2.0  # Documentation generator
sphinx-rtd-theme>=2.0.0  # ReadTheDocs theme
myst-parser>=2.0.0  # Markdown support for Sphinx

# ----- Development servers -----
uvicorn  # ASGI server
gunicorn>=21.2.0  # WSGI server
watchdog>=3.0.0  # File system monitoring

# ----- Utilities -----
json-repair  # Fix malformed JSON
tabulate>=0.9.0  # Pretty table printing
rich>=13.7.0  # Rich terminal output
click>=8.1.0  # CLI creation
tqdm>=4.66.0  # Progress bars

# ==========================================
# OPTIONAL ADVANCED FEATURES
# ==========================================

# ----- Machine Learning (Optional) -----
# tensorflow>=2.15.0  # Deep learning
# torch>=2.1.0  # PyTorch for neural networks
# lightgbm>=4.1.0  # Gradient boosting

# ----- Advanced Weather (Optional) -----
# tropycal>=1.3.0  # Hurricane tracking
# pyproj>=3.6.0  # Map projections
# geopandas>=0.14.0  # Geospatial data

# ----- Backtesting (Optional) -----
# backtrader>=1.9.78  # Backtesting framework
# zipline-reloaded>=3.0.0  # Quantitative backtesting

# ==========================================
# NOTES
# ==========================================
# 1. Install core packages first: pip install -r requirements.txt
# 2. Some packages (cartopy, geopandas) may require system dependencies
# 3. For Windows users: Some packages may need Visual C++ Build Tools
# 4. For production: Consider using pip-tools for dependency locking
# 5. Python version requirement: >= 3.9, tested with 3.13

# ==========================================
# QUICK INSTALL COMMANDS
# ==========================================
# Basic installation:
#   pip install -r requirements.txt
#
# With optional ML features:
#   pip install -r requirements.txt tensorflow torch lightgbm
#
# For development:
#   pip install -r requirements.txt -r requirements-dev.txt
#
# Update all packages:
#   pip install --upgrade -r requirements.txt
