#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Package Installation Test

This script tests if all required packages are properly installed.
Run this after installing requirements to verify your environment.
"""

import sys
import importlib
from typing import List, <PERSON><PERSON>


def test_import(module_name: str) -> Tuple[bool, str]:
    """Test if a module can be imported."""
    try:
        if "." in module_name:
            # Handle submodule imports like metar.Metar
            parts = module_name.split(".")
            module = importlib.import_module(parts[0])
            for part in parts[1:]:
                module = getattr(module, part)
        else:
            importlib.import_module(module_name)
        return True, "OK"
    except ImportError as e:
        return False, str(e)
    except Exception as e:
        return False, f"Error: {e}"


def print_section(title: str):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")


def test_packages():
    """Test all required packages."""
    
    # Core packages
    core_packages = [
        ("httpx", "HTTP client"),
        ("aiohttp", "Async HTTP"),
        ("requests", "HTTP requests"),
        ("pandas", "Data analysis"),
        ("numpy", "Numerical computing"),
        ("scipy", "Scientific computing"),
        ("pydantic", "Data validation"),
        ("cryptography", "Encryption"),
        ("dotenv", "Environment variables"),
    ]
    
    # Weather packages
    weather_packages = [
        ("metar.Metar", "METAR parsing"),
        ("siphon", "NOAA data access"),
        ("metpy", "Weather calculations"),
        ("noaa_sdk", "NOAA SDK"),
        ("pynws", "NWS API client"),
        ("xarray", "Multi-dimensional arrays"),
        ("netCDF4", "NetCDF support"),
        ("diskcache", "Data caching"),
    ]
    
    # AI/LLM packages
    ai_packages = [
        ("openai", "OpenAI API"),
        ("anthropic", "Anthropic API"),
    ]
    
    # GUI packages
    gui_packages = [
        ("tkinter", "GUI framework"),
        ("customtkinter", "Modern tkinter"),
        ("flask", "Web framework"),
        ("flask_cors", "CORS support"),
    ]
    
    # Optional packages
    optional_packages = [
        ("matplotlib", "Plotting"),
        ("cartopy", "Map projections"),
        ("redis", "Redis cache"),
        ("sentry_sdk", "Error tracking"),
        ("loguru", "Enhanced logging"),
        ("rich", "Rich terminal output"),
        ("tqdm", "Progress bars"),
    ]
    
    # Test each category
    categories = [
        ("Core Packages", core_packages),
        ("Weather Packages", weather_packages),
        ("AI/LLM Packages", ai_packages),
        ("GUI Packages", gui_packages),
        ("Optional Packages", optional_packages),
    ]
    
    total_passed = 0
    total_failed = 0
    critical_failures = []
    
    for category_name, packages in categories:
        print_section(category_name)
        category_passed = 0
        category_failed = 0
        
        for module_name, description in packages:
            success, message = test_import(module_name)
            
            if success:
                print(f"✅ {description:25} ({module_name})")
                category_passed += 1
                total_passed += 1
            else:
                print(f"❌ {description:25} ({module_name})")
                print(f"   Error: {message}")
                category_failed += 1
                total_failed += 1
                
                # Mark critical failures
                if category_name in ["Core Packages", "Weather Packages"]:
                    critical_failures.append((module_name, description))
        
        print(f"\n  Category Summary: {category_passed} passed, {category_failed} failed")
    
    # Overall summary
    print_section("Overall Summary")
    print(f"Total packages tested: {total_passed + total_failed}")
    print(f"✅ Passed: {total_passed}")
    print(f"❌ Failed: {total_failed}")
    
    # Critical failures
    if critical_failures:
        print("\n⚠️  CRITICAL FAILURES (must be fixed):")
        for module_name, description in critical_failures:
            print(f"  - {description} ({module_name})")
        print("\nTo fix critical packages:")
        print("  pip install " + " ".join([m.split(".")[0] for m, _ in critical_failures]))
    
    # Recommendations
    print_section("Recommendations")
    
    if total_failed == 0:
        print("🎉 All packages are installed correctly!")
        print("Your environment is ready for the Weather Trading Bot.")
    elif len(critical_failures) == 0:
        print("✅ Core functionality is ready.")
        print("Optional packages can be installed as needed.")
    else:
        print("❌ Critical packages are missing.")
        print("Please install the missing packages before running the bot.")
        print("\nQuick fix command:")
        print("  pip install -r kalshi-ai-trading-bot/requirements.txt")
    
    return len(critical_failures) == 0


def check_python_version():
    """Check Python version compatibility."""
    print_section("Python Version Check")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9+ is required")
        return False
    else:
        print("✅ Python version is compatible")
        return True


def main():
    """Main test function."""
    print("="*60)
    print("  Weather Trading Bot v2.0 - Package Installation Test")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        print("\n⚠️  Please upgrade Python to version 3.9 or higher.")
        return 1
    
    # Test packages
    success = test_packages()
    
    # Next steps
    if success:
        print_section("Next Steps")
        print("1. Configure your API keys:")
        print("   - Edit .env file or run: python settings_manager.py")
        print("\n2. Test weather data access:")
        print("   - Run: python test_weather_data.py")
        print("\n3. Start the trading bot:")
        print("   - Test mode: python deploy_weather_bot.py --mode test")
        print("   - Production: python deploy_weather_bot.py --mode production")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
