"""
Weather Trading Bot v2.0 - Production Deployment Script

This script deploys the complete weather trading system with:
- Environment validation
- Dependency checking
- System initialization
- Health monitoring
- Autonomous trading execution
"""

import asyncio
import os
import sys
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional
import signal

# Add project to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'kalshi-ai-trading-bot'))

from src.utils.database import DatabaseManager
from src.clients.kalshi_client import KalshiClient
from src.clients.xai_client import XAIClient
from src.strategies.weather_trading import (
    WeatherTradingStrategy,
    WeatherTradingConfig,
    run_weather_trading_strategy
)
from src.weather.noaa_client import NOAAClient
from src.weather.weather_analyzer import WeatherAnalyzer
from kalshi_ai_trading_bot.weather_dashboard_extension import WeatherDashboardExtension


class WeatherBotDeployment:
    """
    Production deployment manager for Weather Trading Bot v2.0
    
    Handles:
    - System initialization
    - Environment validation
    - Trading execution
    - Health monitoring
    - Graceful shutdown
    """
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.running = False
        self.components = {}
        
    def _setup_logging(self) -> logging.Logger:
        """Setup production logging."""
        logger = logging.getLogger('weather_bot_deployment')
        logger.setLevel(logging.INFO)
        
        # File handler
        fh = logging.FileHandler('weather_bot_production.log')
        fh.setLevel(logging.INFO)
        
        # Console handler
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        fh.setFormatter(formatter)
        ch.setFormatter(formatter)
        
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        return logger
    
    def validate_environment(self) -> bool:
        """Validate environment setup."""
        self.logger.info("🔍 Validating Environment...")
        
        errors = []
        
        # Check API keys
        if not os.getenv('KALSHI_API_KEY'):
            errors.append("❌ KALSHI_API_KEY not set")
        else:
            self.logger.info("✅ Kalshi API key found")
        
        if not os.getenv('XAI_API_KEY') and not os.getenv('OPENAI_API_KEY'):
            self.logger.warning("⚠️ No AI API key found (XAI_API_KEY or OPENAI_API_KEY)")
        else:
            self.logger.info("✅ AI API key found")
        
        # Check Python version
        if sys.version_info < (3, 8):
            errors.append(f"❌ Python 3.8+ required (current: {sys.version})")
        else:
            self.logger.info(f"✅ Python version: {sys.version.split()[0]}")
        
        # Check dependencies
        required_packages = [
            'pandas', 'numpy', 'requests', 'asyncio',
            'metar', 'siphon', 'metpy'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            errors.append(f"❌ Missing packages: {', '.join(missing_packages)}")
        else:
            self.logger.info("✅ All required packages installed")
        
        # Check NOAA connectivity
        self.logger.info("Testing NOAA data access...")
        noaa_client = NOAAClient()
        try:
            loop = asyncio.get_event_loop()
            obs = loop.run_until_complete(
                noaa_client.get_current_observation('KNYC')
            )
            if obs:
                self.logger.info(f"✅ NOAA data accessible (NYC: {obs.temperature_f:.1f}°F)")
            else:
                errors.append("❌ Cannot retrieve NOAA data")
        except Exception as e:
            errors.append(f"❌ NOAA connection error: {e}")
        
        # Report results
        if errors:
            self.logger.error("Environment validation failed:")
            for error in errors:
                self.logger.error(f"  {error}")
            return False
        
        self.logger.info("✅ Environment validation complete!")
        return True
    
    async def initialize_components(self) -> bool:
        """Initialize all trading components."""
        self.logger.info("🚀 Initializing Trading Components...")
        
        try:
            # Database
            self.logger.info("Initializing database...")
            self.components['db_manager'] = DatabaseManager()
            
            # Kalshi client
            self.logger.info("Connecting to Kalshi...")
            self.components['kalshi_client'] = KalshiClient()
            
            # AI client
            self.logger.info("Initializing AI client...")
            self.components['xai_client'] = XAIClient()
            
            # Weather components
            self.logger.info("Setting up weather components...")
            self.components['noaa_client'] = NOAAClient()
            self.components['weather_analyzer'] = WeatherAnalyzer(
                self.components['xai_client'],
                self.components['noaa_client']
            )
            
            # Trading strategy
            self.logger.info("Configuring trading strategy...")
            config = WeatherTradingConfig(
                min_edge_threshold=0.08,
                max_weather_exposure=0.50,
                min_hours_to_expiry=3,
                sensor_anomaly_threshold=5.0,
                cli_verification_enabled=True,
                backup_station_validation=True
            )
            
            self.components['weather_strategy'] = WeatherTradingStrategy(
                self.components['db_manager'],
                self.components['kalshi_client'],
                self.components['xai_client'],
                config
            )
            
            # Initialize with balance
            await self.components['weather_strategy'].async_initialize()
            
            # Dashboard
            self.logger.info("Setting up dashboard...")
            self.components['dashboard'] = WeatherDashboardExtension()
            await self.components['dashboard'].initialize(self.components['xai_client'])
            
            self.logger.info("✅ All components initialized successfully!")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            return False
    
    async def run_trading_cycle(self):
        """Execute one complete trading cycle."""
        self.logger.info(f"⚡ Trading Cycle Started - {datetime.now()}")
        
        try:
            # Get weather observations
            observations = await self.components['noaa_client'].get_all_station_observations()
            self.logger.info(f"📊 Retrieved {len(observations)} weather observations")
            
            # Display current temperatures
            for station_id, obs in observations.items():
                self.logger.info(f"  {station_id}: {obs.temperature_f:.1f}°F")
            
            # Execute trading strategy
            results = await self.components['weather_strategy'].execute_weather_trading_strategy()
            
            # Log results
            self.logger.info(
                f"📈 Trading Results: "
                f"{results.total_positions} positions, "
                f"${results.total_capital_used:.0f} deployed, "
                f"Expected return: {results.portfolio_expected_return:.2%}"
            )
            
            # Monitor existing positions
            await self.components['weather_strategy']._monitor_weather_positions()
            
        except Exception as e:
            self.logger.error(f"Error in trading cycle: {e}")
    
    async def display_dashboard(self):
        """Display weather trading dashboard."""
        try:
            await self.components['dashboard'].display_full_weather_dashboard()
        except Exception as e:
            self.logger.error(f"Dashboard error: {e}")
    
    async def health_check(self) -> Dict:
        """Perform system health check."""
        health = {
            'timestamp': datetime.now().isoformat(),
            'status': 'healthy',
            'components': {}
        }
        
        # Check each component
        for name, component in self.components.items():
            try:
                if name == 'noaa_client':
                    # Test NOAA connectivity
                    obs = await component.get_current_observation('KNYC')
                    health['components'][name] = 'healthy' if obs else 'degraded'
                elif name == 'kalshi_client':
                    # Test Kalshi connectivity
                    balance = await component.get_balance()
                    health['components'][name] = 'healthy' if balance else 'degraded'
                else:
                    health['components'][name] = 'healthy'
            except Exception as e:
                health['components'][name] = f'error: {str(e)[:50]}'
                health['status'] = 'degraded'
        
        return health
    
    async def run_production(self, interval_minutes: int = 15):
        """
        Run production trading system.
        
        Args:
            interval_minutes: Minutes between trading cycles
        """
        self.logger.info("🌡️ WEATHER TRADING BOT v2.0 - PRODUCTION MODE")
        self.logger.info("=" * 60)
        
        # Validate environment
        if not self.validate_environment():
            self.logger.error("Environment validation failed. Exiting.")
            return
        
        # Initialize components
        if not await self.initialize_components():
            self.logger.error("Component initialization failed. Exiting.")
            return
        
        self.running = True
        self.logger.info(f"✅ System started. Trading every {interval_minutes} minutes.")
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        cycle_count = 0
        
        while self.running:
            try:
                cycle_count += 1
                self.logger.info(f"\n{'='*60}")
                self.logger.info(f"CYCLE #{cycle_count} - {datetime.now()}")
                self.logger.info("="*60)
                
                # Health check
                health = await self.health_check()
                if health['status'] != 'healthy':
                    self.logger.warning(f"System health degraded: {health}")
                
                # Trading cycle
                await self.run_trading_cycle()
                
                # Dashboard update (every 3rd cycle)
                if cycle_count % 3 == 0:
                    await self.display_dashboard()
                
                # Wait for next cycle
                if self.running:
                    self.logger.info(f"💤 Waiting {interval_minutes} minutes until next cycle...")
                    await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                self.logger.error(f"Error in production loop: {e}")
                await asyncio.sleep(60)  # Brief pause before retry
        
        self.logger.info("🛑 Weather Trading Bot shutdown complete.")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}. Shutting down gracefully...")
        self.running = False
    
    async def run_test_mode(self):
        """Run in test mode with single cycle."""
        self.logger.info("🧪 WEATHER TRADING BOT v2.0 - TEST MODE")
        self.logger.info("=" * 60)
        
        # Validate environment
        if not self.validate_environment():
            return
        
        # Initialize
        if not await self.initialize_components():
            return
        
        # Run single cycle
        await self.run_trading_cycle()
        
        # Display dashboard
        await self.display_dashboard()
        
        # Health check
        health = await self.health_check()
        self.logger.info(f"System Health: {health}")
        
        self.logger.info("✅ Test mode complete!")


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Weather Trading Bot v2.0 - Production Deployment'
    )
    parser.add_argument(
        '--mode',
        choices=['production', 'test', 'validate'],
        default='test',
        help='Deployment mode'
    )
    parser.add_argument(
        '--interval',
        type=int,
        default=15,
        help='Trading interval in minutes (production mode)'
    )
    
    args = parser.parse_args()
    
    deployment = WeatherBotDeployment()
    
    if args.mode == 'production':
        await deployment.run_production(args.interval)
    elif args.mode == 'test':
        await deployment.run_test_mode()
    elif args.mode == 'validate':
        if deployment.validate_environment():
            print("✅ Environment validation successful!")
        else:
            print("❌ Environment validation failed!")
            sys.exit(1)


if __name__ == "__main__":
    print("""
    ╔══════════════════════════════════════════════════════════╗
    ║        WEATHER TRADING BOT v2.0 - DEPLOYMENT            ║
    ║                                                          ║
    ║  Integrating Meteorological Intelligence with           ║
    ║  Kalshi Weather Derivatives Trading                     ║
    ╚══════════════════════════════════════════════════════════╝
    """)
    
    asyncio.run(main())
