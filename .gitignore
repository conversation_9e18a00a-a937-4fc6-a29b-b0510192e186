# Security - NEVER commit these
.env
.env.local
.env.production
.env.*
!.env.security_template
*.key
*.pem
*.cert
*.crt
credentials.json
secrets.json
config.json
kalshi_credentials.json
noaa_api_keys.json

# API Keys and Tokens
api_keys/
tokens/
auth/

# Audit Results (may contain sensitive info)
audit_results.md
security_scan_*.txt
vulnerability_report_*.json

# Kalshi Account Data
kalshi_account_data/
trading_history/
positions_*.json
orders_*.json
wallet_*.json

# Weather Data Cache (can be large)
weather_cache/
historical_data/
*.nc
*.grib
*.grib2

# Backtest Results (can be large)
backtest_results/
backtest_cache/
simulation_results/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
ENV/
env/
.venv/
weather_bot_env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Jupyter Notebooks
.ipynb_checkpoints
*.ipynb

# Logs
logs/
*.log
*.log.*
trading_logs/
error_logs/

# Database
*.db
*.sqlite
*.sqlite3
weather_data.db
trading_history.db

# Temporary Files
tmp/
temp/
*.tmp
*.temp
.cache/

# Model Files (can be large)
models/
*.pkl
*.joblib
*.h5
*.pt
*.pth

# Documentation builds
docs/_build/
docs/.doctrees/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Operating System Files
Thumbs.db
desktop.ini

# Streamlit
.streamlit/secrets.toml
streamlit_cache/

# Project Specific
weather_bot_state.json
trading_state.json
portfolio_snapshot_*.json
performance_metrics_*.csv
risk_metrics_*.json

# Deployment
deploy_config.json
production_settings.json
*.tfstate
*.tfstate.backup
.terraform/
