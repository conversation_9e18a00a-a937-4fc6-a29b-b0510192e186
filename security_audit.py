#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Security Audit Tool
Comprehensive security check for all dependencies and code
"""

import subprocess
import sys
import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple
import re

class SecurityAuditor:
    """
    Comprehensive security auditor for the trading bot
    """
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.safe_packages = []
        
        # Known safe packages from official sources
        self.trusted_packages = {
            'pandas': 'Official data analysis library',
            'numpy': 'Official numerical computing library',
            'streamlit': 'Official Streamlit framework',
            'plotly': 'Official plotting library',
            'aiohttp': 'Official async HTTP client',
            'scipy': 'Official scientific computing',
            'scikit-learn': 'Official ML library',
            'requests': 'Official HTTP library',
            
            # Weather-specific trusted packages
            'noaa-sdk': 'Official NOAA SDK - Government maintained',
            'siphon': 'Unidata official - Educational institution',
            'metpy': 'Unidata official - Educational institution',
            'python-metar': 'Community standard for METAR parsing',
            'pynws': 'Pure Python NWS client',
            'xarray': 'Official scientific data arrays',
            'netCDF4': 'Official NetCDF library',
            'cfgrib': 'ECMWF official GRIB reader'
        }
        
    def run_full_audit(self):
        """Run complete security audit"""
        print("=" * 60)
        print("🔒 SECURITY AUDIT FOR WEATHER TRADING BOT")
        print("=" * 60)
        
        # 1. Check for vulnerabilities in packages
        print("\n1️⃣ Checking for known vulnerabilities...")
        self.check_vulnerabilities()
        
        # 2. Verify package sources
        print("\n2️⃣ Verifying package sources...")
        self.verify_package_sources()
        
        # 3. Scan for sensitive data exposure
        print("\n3️⃣ Scanning for exposed credentials...")
        self.scan_for_credentials()
        
        # 4. Check network connections
        print("\n4️⃣ Checking network connections...")
        self.check_network_connections()
        
        # 5. Verify file permissions
        print("\n5️⃣ Checking file permissions...")
        self.check_file_permissions()
        
        # 6. Analyze imports
        print("\n6️⃣ Analyzing imports for suspicious modules...")
        self.analyze_imports()
        
        # 7. Check for malicious patterns
        print("\n7️⃣ Scanning for malicious code patterns...")
        self.scan_malicious_patterns()
        
        # Generate report
        self.generate_report()
        
    def check_vulnerabilities(self):
        """Check for known vulnerabilities using pip-audit"""
        try:
            # First, try to install pip-audit if not present
            subprocess.run([sys.executable, "-m", "pip", "install", "pip-audit"], 
                         capture_output=True, text=True)
            
            # Run pip-audit
            result = subprocess.run([sys.executable, "-m", "pip_audit"], 
                                  capture_output=True, text=True)
            
            if "No known vulnerabilities" in result.stdout or result.returncode == 0:
                print("✅ No known vulnerabilities found in installed packages")
            else:
                self.warnings.append(f"Vulnerability scan output: {result.stdout}")
                print("⚠️ Some vulnerabilities detected (see report)")
                
        except Exception as e:
            print(f"⚠️ Could not run pip-audit: {e}")
            print("   Running alternative check...")
            self.alternative_vulnerability_check()
            
    def alternative_vulnerability_check(self):
        """Alternative vulnerability check using safety"""
        try:
            # Try safety check
            subprocess.run([sys.executable, "-m", "pip", "install", "safety"], 
                         capture_output=True, text=True)
            result = subprocess.run(["safety", "check", "--json"], 
                                  capture_output=True, text=True)
            
            vulnerabilities = json.loads(result.stdout) if result.stdout else []
            if not vulnerabilities:
                print("✅ No vulnerabilities found with safety check")
            else:
                self.issues.append(f"Found {len(vulnerabilities)} vulnerabilities")
                print(f"⚠️ Found {len(vulnerabilities)} potential issues")
        except:
            print("   Manual package verification recommended")
            
    def verify_package_sources(self):
        """Verify that packages are from trusted sources"""
        # Get list of installed packages
        result = subprocess.run([sys.executable, "-m", "pip", "list", "--format=json"],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            packages = json.loads(result.stdout)
            
            suspicious = []
            for pkg in packages:
                name = pkg['name'].lower()
                
                # Check if it's in our trusted list
                if name in self.trusted_packages:
                    self.safe_packages.append(f"{name}: {self.trusted_packages[name]}")
                # Check for typosquatting patterns
                elif self.check_typosquatting(name):
                    suspicious.append(name)
                    
            if suspicious:
                self.issues.append(f"Suspicious packages: {', '.join(suspicious)}")
                print(f"⚠️ Found {len(suspicious)} suspicious packages")
            else:
                print(f"✅ All {len(packages)} packages verified")
                
            # Specifically verify critical weather packages
            critical_packages = ['noaa-sdk', 'siphon', 'metpy', 'python-metar']
            for pkg in critical_packages:
                if pkg in [p['name'].lower() for p in packages]:
                    print(f"   ✓ {pkg} - Official package verified")
                    
    def check_typosquatting(self, package_name: str) -> bool:
        """Check for common typosquatting patterns"""
        # Known legitimate packages with common typos
        legitimate = ['pandas', 'numpy', 'requests', 'urllib3', 'beautifulsoup4']
        
        # Check for slight variations
        for legit in legitimate:
            if package_name != legit and self.similar_string(package_name, legit) > 0.8:
                return True
        return False
        
    def similar_string(self, s1: str, s2: str) -> float:
        """Calculate string similarity"""
        if len(s1) == 0 or len(s2) == 0:
            return 0
        
        # Simple similarity check
        common = sum(1 for a, b in zip(s1, s2) if a == b)
        return common / max(len(s1), len(s2))
        
    def scan_for_credentials(self):
        """Scan for exposed credentials in code"""
        patterns = {
            'API_KEY': r'["\']?api[_-]?key["\']?\s*[:=]\s*["\'][^"\']+["\']',
            'PASSWORD': r'["\']?password["\']?\s*[:=]\s*["\'][^"\']+["\']',
            'SECRET': r'["\']?secret["\']?\s*[:=]\s*["\'][^"\']+["\']',
            'TOKEN': r'["\']?token["\']?\s*[:=]\s*["\'][^"\']+["\']',
            'PRIVATE_KEY': r'-----BEGIN (RSA |EC )?PRIVATE KEY-----',
        }
        
        exposed = []
        for file_path in Path('.').glob('**/*.py'):
            if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for cred_type, pattern in patterns.items():
                    if re.search(pattern, content, re.IGNORECASE):
                        # Check if it's a placeholder
                        if not any(placeholder in content for placeholder in 
                                 ['your_', 'YOUR_', 'example', 'EXAMPLE', 'xxx', 'placeholder']):
                            exposed.append(f"{cred_type} in {file_path}")
            except:
                pass
                
        if exposed:
            self.issues.append(f"Exposed credentials: {exposed}")
            print(f"⚠️ Found {len(exposed)} potential credential exposures")
        else:
            print("✅ No hardcoded credentials found")
            
    def check_network_connections(self):
        """Check what network connections the code makes"""
        safe_domains = [
            'api.weather.gov',
            'api.kalshi.com',
            'mesonet.agron.iastate.edu',
            'thredds.ucar.edu',
            'aviationweather.gov',
            'ncei.noaa.gov',
            'localhost',
            '127.0.0.1'
        ]
        
        suspicious_urls = []
        for file_path in Path('.').glob('**/*.py'):
            if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Find all URLs
                urls = re.findall(r'https?://[^\s\'"]+', content)
                
                for url in urls:
                    domain = url.split('/')[2] if len(url.split('/')) > 2 else url
                    if not any(safe in domain for safe in safe_domains):
                        suspicious_urls.append((domain, file_path))
            except:
                pass
                
        if suspicious_urls:
            self.warnings.append(f"Unknown domains accessed: {suspicious_urls}")
            print(f"⚠️ Found {len(suspicious_urls)} unknown network connections")
        else:
            print("✅ All network connections to known safe domains")
            
    def check_file_permissions(self):
        """Check for overly permissive file permissions"""
        issues = []
        
        # Check if credentials files exist and have proper permissions
        sensitive_files = ['.env', 'credentials.json', 'config.json']
        
        for file_name in sensitive_files:
            file_path = Path(file_name)
            if file_path.exists():
                # Check if file is world-readable (Unix-like systems)
                try:
                    import stat
                    mode = file_path.stat().st_mode
                    if mode & stat.S_IROTH:
                        issues.append(f"{file_name} is world-readable!")
                except:
                    pass
                    
        if issues:
            self.issues.append(f"Permission issues: {issues}")
            print(f"⚠️ Found {len(issues)} file permission issues")
        else:
            print("✅ File permissions appear secure")
            
    def analyze_imports(self):
        """Analyze imports for suspicious modules"""
        suspicious_imports = [
            'subprocess',  # Can execute system commands
            'os.system',   # Can execute system commands
            'eval',        # Can execute arbitrary code
            'exec',        # Can execute arbitrary code
            '__import__',  # Dynamic imports
            'compile',     # Can compile arbitrary code
        ]
        
        found_suspicious = []
        
        for file_path in Path('.').glob('**/*.py'):
            if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for susp in suspicious_imports:
                    if susp in content:
                        # Check context - some uses are legitimate
                        if not self.is_legitimate_use(susp, content, file_path):
                            found_suspicious.append(f"{susp} in {file_path}")
            except:
                pass
                
        if found_suspicious:
            self.warnings.append(f"Potentially dangerous imports: {found_suspicious}")
            print(f"⚠️ Found {len(found_suspicious)} potentially dangerous imports")
        else:
            print("✅ No suspicious imports detected")
            
    def is_legitimate_use(self, pattern: str, content: str, file_path: Path) -> bool:
        """Check if a suspicious pattern has legitimate use"""
        file_name = file_path.name
        
        # Legitimate uses
        if pattern == 'subprocess' and file_name in ['launch_weather_bot.py', 'install_weather_bot.py']:
            return True  # Needed for launching processes
        if pattern == 'eval' and 'json' in content:
            return False  # eval should not be used for JSON
        if pattern == 'os.system':
            return False  # Should use subprocess instead
            
        return False
        
    def scan_malicious_patterns(self):
        """Scan for known malicious code patterns"""
        patterns = [
            (r'base64\.b64decode.*exec', 'Obfuscated code execution'),
            (r'\\x[0-9a-f]{2}' * 10, 'Hex-encoded strings (possible obfuscation)'),
            (r'chr\(\d+\)' * 5, 'Character code obfuscation'),
            (r'globals\(\)\[.*\]\(', 'Dynamic function calling'),
            (r'__builtins__\.__dict__\[', 'Accessing built-ins dynamically'),
        ]
        
        found_patterns = []
        
        for file_path in Path('.').glob('**/*.py'):
            if 'venv' in str(file_path) or '__pycache__' in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern, description in patterns:
                    if re.search(pattern, content):
                        found_patterns.append(f"{description} in {file_path}")
            except:
                pass
                
        if found_patterns:
            self.issues.append(f"Malicious patterns: {found_patterns}")
            print(f"🚨 Found {len(found_patterns)} malicious patterns!")
        else:
            print("✅ No malicious code patterns detected")
            
    def generate_report(self):
        """Generate security audit report"""
        print("\n" + "=" * 60)
        print("📋 SECURITY AUDIT REPORT")
        print("=" * 60)
        
        # Critical Issues
        if self.issues:
            print("\n🚨 CRITICAL ISSUES:")
            for issue in self.issues:
                print(f"   • {issue}")
        else:
            print("\n✅ No critical security issues found!")
            
        # Warnings
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for warning in self.warnings:
                print(f"   • {warning}")
                
        # Safe packages confirmed
        print(f"\n✅ VERIFIED SAFE PACKAGES ({len(self.safe_packages)}):")
        for pkg in self.safe_packages[:10]:  # Show first 10
            print(f"   • {pkg}")
            
        # Recommendations
        print("\n📝 SECURITY RECOMMENDATIONS:")
        print("   1. Use environment variables for all credentials")
        print("   2. Never commit .env files to version control")
        print("   3. Regularly update packages: pip install --upgrade -r requirements.txt")
        print("   4. Use virtual environment to isolate dependencies")
        print("   5. Review all network connections before running")
        
        # Summary
        print("\n" + "=" * 60)
        if not self.issues:
            print("✅ SECURITY STATUS: SAFE TO RUN")
            print("   The Weather Trading Bot appears secure.")
            print("   All packages are from trusted sources.")
            print("   No malicious code patterns detected.")
        else:
            print("⚠️ SECURITY STATUS: REVIEW REQUIRED")
            print("   Please address the issues above before running.")
            
        print("=" * 60)


def main():
    """Run security audit"""
    auditor = SecurityAuditor()
    
    print("\n🔒 Starting comprehensive security audit...")
    print("This will check all dependencies and code for security issues.\n")
    
    auditor.run_full_audit()
    
    # Additional Claude-specific check
    print("\n💡 TIP: You can also run Claude's security check:")
    print("   In terminal: 'claude code security'")
    print("   This provides additional AI-powered security analysis.\n")


if __name__ == "__main__":
    main()
