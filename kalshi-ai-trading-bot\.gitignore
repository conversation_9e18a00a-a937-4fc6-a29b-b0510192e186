# Environment variables and secrets
.env
.env.local
.env.production
.env.staging
*.key
kalshi_private_key
secrets.json
config.json

# Database files
*.db
*.sqlite
*.sqlite3
trading_system.db
e2e_test_trading_system.db

# Logs
logs/
*.log
trading_system_*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Performance analysis files
performance_analysis_*.json
performance_analysis_report.json
performance_analysis_*.txt
performance_analysis_*.log

# Grok analysis files
grok4_full_analysis.txt
grok4_analysis_*.txt
grok4_analysis_*.json

# Test database files
test_*.db
e2e_test_*.db
*_test.db

# Temporary analysis files
*_analysis_*.json
*_analysis_*.txt
*_analysis_*.log

# Dashboard temporary files
dashboard_*.log
dashboard_*.json

# Cursor IDE
.cursor/

# Temporary files
*.tmp
*.temp
*~

# API keys and credentials
api_keys.txt
credentials.json
secrets.yaml 