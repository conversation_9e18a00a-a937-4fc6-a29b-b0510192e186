#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Easy Launcher
One-click launcher for the complete trading system with UI
"""

import subprocess
import sys
import os
from pathlib import Path
import webbrowser
import time
import argparse

def check_requirements():
    """Check if all required packages are installed"""
    required = ['streamlit', 'pandas', 'plotly', 'aiohttp']
    missing = []
    
    for package in required:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"⚠️  Missing packages: {', '.join(missing)}")
        print("Installing missing packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing)
        print("✅ Packages installed successfully!")

def launch_streamlit():
    """Launch the Streamlit dashboard"""
    print("🚀 Launching Weather Trading Bot Dashboard...")
    print("=" * 60)
    
    # Get the streamlit script path
    dashboard_path = Path(__file__).parent / "streamlit_trading_dashboard.py"
    
    # Launch Streamlit
    cmd = [sys.executable, "-m", "streamlit", "run", str(dashboard_path), 
           "--server.port", "8501",
           "--server.address", "localhost",
           "--browser.gatherUsageStats", "false"]
    
    # Start the process
    process = subprocess.Popen(cmd)
    
    # Wait a moment for server to start
    time.sleep(3)
    
    # Open browser automatically
    webbrowser.open("http://localhost:8501")
    
    print("✅ Dashboard launched successfully!")
    print("🌐 Open http://localhost:8501 in your browser")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")
    
    try:
        process.wait()
    except KeyboardInterrupt:
        print("\n⏹️  Shutting down...")
        process.terminate()
        print("✅ Server stopped")

def launch_cli(mode):
    """Launch the CLI version of the trading system"""
    print(f"🚀 Launching Weather Trading Bot in {mode} mode...")
    
    # Get the unified system path
    system_path = Path(__file__).parent / "unified_trading_system.py"
    
    # Launch the system
    cmd = [sys.executable, str(system_path), mode]
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⏹️  Trading stopped")

def main():
    parser = argparse.ArgumentParser(description='Weather Trading Bot v2.0 Launcher')
    parser.add_argument(
        '--mode',
        choices=['ui', 'live', 'paper', 'backtest'],
        default='ui',
        help='Launch mode: ui (Streamlit dashboard), live, paper, or backtest'
    )
    
    args = parser.parse_args()
    
    print("""
    ╔══════════════════════════════════════════════════════════╗
    ║           🌤️  Weather Trading Bot v2.0                    ║
    ║         Intelligent Weather Derivatives Trading           ║
    ╚══════════════════════════════════════════════════════════╝
    """)
    
    # Check requirements
    check_requirements()
    
    if args.mode == 'ui':
        launch_streamlit()
    else:
        launch_cli(args.mode.upper())

if __name__ == "__main__":
    main()
