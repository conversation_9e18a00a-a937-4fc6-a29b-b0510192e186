"""
Standalone test script for weather data retrieval.

Tests NOAA client functionality with real data.
"""

import asyncio
import sys
import os

# Add the kalshi-ai-trading-bot/src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'kalshi-ai-trading-bot', 'src'))

# Mock the logging setup if it doesn't exist
try:
    from utils.logging_setup import get_trading_logger
except ImportError:
    import logging
    def get_trading_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger
    
    # Monkey patch it
    import sys
    sys.modules['src.utils.logging_setup'] = type(sys)('logging_setup')
    sys.modules['src.utils.logging_setup'].get_trading_logger = get_trading_logger

from weather.noaa_client import NOAAClient

async def test_weather_data():
    """Test weather data retrieval with real NOAA data."""
    
    print("=" * 60)
    print("WEATHER DATA RETRIEVAL TEST")
    print("=" * 60)
    
    client = NOAAClient()
    
    # Test 1: Get current observations for key stations
    print("\n📍 Testing Current Weather Observations...")
    print("-" * 40)
    
    test_stations = ['KNYC', 'KMIA', 'KMDW', 'KDEN', 'KLAX']
    
    for station_id in test_stations:
        try:
            obs = await client.get_current_observation(station_id)
            if obs:
                station_info = client.WEATHER_STATIONS[station_id]
                print(f"✅ {station_id} ({station_info.name}):")
                print(f"   Temperature: {obs.temperature_f:.1f}°F")
                print(f"   Conditions: {obs.sky_conditions}")
                print(f"   Wind: {obs.wind_speed:.0f} kt from {obs.wind_direction}°")
                if obs.twenty_four_hour_max:
                    print(f"   24hr Max: {obs.twenty_four_hour_max:.1f}°F")
            else:
                print(f"⚠️ {station_id}: No data available")
        except Exception as e:
            print(f"❌ {station_id}: Error - {e}")
    
    # Test 2: Get AFD for New York
    print("\n📄 Testing Area Forecast Discussion (AFD)...")
    print("-" * 40)
    
    try:
        afd = await client.get_afd('OKX')  # New York WFO
        if afd:
            print(f"✅ AFD from {afd.office_code}:")
            print(f"   Issue Time: {afd.issue_time}")
            print(f"   Confidence: {afd.confidence_score:.0f}%")
            print(f"   Pattern: {afd.pattern_type}")
            print(f"   Model Preferences: {list(afd.model_preferences.keys())}")
            if afd.concerns:
                print(f"   Concerns: {afd.concerns[:2]}")  # Show first 2 concerns
        else:
            print("⚠️ No AFD data available")
    except Exception as e:
        print(f"❌ AFD Error: {e}")
    
    # Test 3: Get model consensus (mock data for now)
    print("\n📊 Testing Model Consensus...")
    print("-" * 40)
    
    try:
        nyc_station = client.WEATHER_STATIONS['KNYC']
        consensus = await client.get_model_consensus(nyc_station, hour=12)
        
        if consensus:
            print(f"✅ 12-hour forecast consensus for NYC:")
            for model, temp in consensus.items():
                print(f"   {model}: {temp:.1f}°F")
            
            avg_temp = sum(consensus.values()) / len(consensus)
            spread = max(consensus.values()) - min(consensus.values())
            print(f"   Average: {avg_temp:.1f}°F")
            print(f"   Spread: {spread:.1f}°F")
    except Exception as e:
        print(f"❌ Model Consensus Error: {e}")
    
    # Test 4: Summary
    print("\n" + "=" * 60)
    print("📈 WEATHER DATA TEST SUMMARY")
    print("=" * 60)
    
    all_obs = await client.get_all_station_observations()
    print(f"✅ Successfully retrieved data from {len(all_obs)}/{len(client.WEATHER_STATIONS)} stations")
    
    if all_obs:
        temps = [obs.temperature_f for obs in all_obs.values() if obs.temperature_f]
        if temps:
            print(f"📊 Temperature Range: {min(temps):.1f}°F to {max(temps):.1f}°F")
            print(f"📊 Average Temperature: {sum(temps)/len(temps):.1f}°F")
    
    print("\n✅ Weather data retrieval test complete!")
    print("📝 Note: This uses real NOAA data, so values will vary.")

if __name__ == "__main__":
    print("Starting weather data test...")
    asyncio.run(test_weather_data())
