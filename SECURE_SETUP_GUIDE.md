# Weather Trading Bot v2.0 - Secure Setup Guide

## 🔐 No More Hardcoded API Keys!

This bot now includes a **Secure Settings Manager** that encrypts and safely stores your API keys, so you never need to hardcode them or expose them in your code.

## 📋 Quick Setup Steps

### Step 1: Install Required Security Package
```bash
pip install cryptography colorama
```

### Step 2: Launch the Settings Manager GUI
```bash
python settings_manager.py
```

This opens a user-friendly interface where you can:
- ✅ Enter your API keys securely (they're masked as you type)
- ✅ Configure trading parameters
- ✅ Test your connections
- ✅ Save everything encrypted

### Step 3: Enter Your API Keys

#### In the Settings Manager GUI:

1. **API Keys Tab:**
   - **Kalshi API Key**: Your Kalshi API key
   - **Kalshi API Secret**: Your Kalshi API secret
   - **Environment**: Select "demo" for testing or "production" for live trading
   - **XAI API Key**: Your XAI (Grok) API key for LLM analysis
   - **OpenAI API Key**: (Optional) Alternative LLM provider

2. **Trading Settings Tab:**
   - Configure risk limits
   - Set position sizes
   - Choose default trading mode

3. **Click "Save Settings"**
   - Your keys are encrypted with military-grade encryption
   - Stored in `~/.weather_trading_bot/config.enc`
   - Only accessible by your user account

### Step 4: Test Your Connections
Click the "Test Connections" button to verify everything works!

### Step 5: Run the Bot
```bash
# Using the secure deployment script
python deploy_weather_bot_secure.py

# Or run the quick start
python quick_start.py
```

---

## 🛡️ Security Features

### How Your Keys Are Protected

1. **256-bit Encryption**: Your API keys are encrypted using Fernet (symmetric encryption)
2. **Secure Storage**: Encrypted file stored in your home directory with restricted permissions
3. **Memory Protection**: Keys are only decrypted when needed and cleared from memory after use
4. **No Plain Text**: Keys are never stored in plain text anywhere

### File Locations
- **Encrypted Config**: `~/.weather_trading_bot/config.enc`
- **Encryption Key**: `~/.weather_trading_bot/.key` (protected with 0600 permissions on Unix)

---

## 📺 Settings Manager Interface

### GUI Features

#### 1. API Keys Tab
- Enter Kalshi credentials
- Configure LLM provider (XAI, OpenAI, Anthropic)
- Toggle password visibility with 👁 button
- All entries are masked by default

#### 2. Trading Settings Tab
- **Max Position Size**: Default $25,000 (Kalshi limit)
- **Daily Loss Limit**: Default 5%
- **Weather Exposure**: Default 40% of portfolio
- **Minimum Edge**: Default 8%
- **Update Frequency**: Default 15 minutes

#### 3. Advanced Tab
- Enable/disable data sources (AFD, Models, Enhanced Analytics)
- Configure logging level
- Set dashboard port
- Email alerts configuration

#### 4. Status Tab
- Test all connections
- View connection status
- Troubleshoot issues

---

## 🚀 Three Ways to Run the Bot

### Option 1: GUI Setup + Secure Deployment
```bash
# Step 1: Configure settings via GUI
python settings_manager.py

# Step 2: Run with secure deployment
python deploy_weather_bot_secure.py --mode production
```

### Option 2: Command Line Setup
```bash
# Create a .env file (less secure but convenient)
cat > .env << EOF
KALSHI_API_KEY=your_key_here
KALSHI_API_SECRET=your_secret_here
XAI_API_KEY=your_xai_key_here
KALSHI_ENV=production
EOF

# Run the original deployment
python deploy_weather_bot.py
```

### Option 3: Export from Settings Manager
```bash
# Export your secure settings to environment
python settings_manager.py --export

# Then run any script
python deploy_weather_bot.py
```

---

## 💡 Command Line Options

### Settings Manager CLI
```bash
# Export settings to environment variables
python settings_manager.py --export

# Show current settings (keys hidden)
python settings_manager.py --show

# Launch GUI (default)
python settings_manager.py
```

### Deployment Options
```bash
# Test mode - single cycle
python deploy_weather_bot_secure.py --mode test

# Paper trading (default)
python deploy_weather_bot_secure.py --mode paper

# Production with custom interval
python deploy_weather_bot_secure.py --mode production --interval 10
```

---

## 🔧 Troubleshooting

### "No settings found!"
**Solution**: Run `python settings_manager.py` to configure your API keys first.

### "Missing required settings"
**Solution**: You need at least:
- Kalshi API Key
- Kalshi API Secret  
- One LLM key (XAI or OpenAI)

### Can't see the GUI on WSL/Linux
**Solution**: Install tkinter:
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# Fedora
sudo dnf install python3-tkinter

# macOS (should be included)
# If not: brew install python-tk
```

### Want to reset settings
```bash
# Remove the encrypted config
rm -rf ~/.weather_trading_bot

# Run settings manager again
python settings_manager.py
```

---

## 🎯 Best Practices

### DO ✅
- Use the Settings Manager GUI for initial setup
- Test connections before going to production
- Start with paper trading mode
- Keep your `.key` file backed up securely
- Use environment-specific settings (demo vs production)

### DON'T ❌
- Never commit API keys to git
- Don't share your `~/.weather_trading_bot` directory
- Don't hardcode keys in scripts
- Don't use production keys for testing

---

## 📊 Settings Priority Order

The bot checks for settings in this order:
1. **Encrypted Settings** (via Settings Manager) - Most Secure ✅
2. **Environment Variables** (via export or .env file)
3. **Command Line Arguments** (for mode and interval)

---

## 🆘 Help & Support

### Get Current Configuration
```python
from settings_manager import SecureSettingsManager

manager = SecureSettingsManager()
settings = manager.load_settings()

# View settings (sensitive data hidden)
for key, value in settings.items():
    if 'KEY' in key or 'SECRET' in key:
        print(f"{key}: ***hidden***")
    else:
        print(f"{key}: {value}")
```

### Programmatic Settings Update
```python
from settings_manager import SecureSettingsManager

manager = SecureSettingsManager()
settings = manager.load_settings()

# Update a setting
settings['UPDATE_FREQUENCY'] = '30'
settings['TRADING_MODE'] = 'production'

# Save encrypted
manager.save_settings(settings)
```

---

## 🎉 You're Ready!

Your API keys are now:
- ✅ Securely encrypted
- ✅ Safely stored
- ✅ Never hardcoded
- ✅ Easy to manage

Run `python settings_manager.py` to get started with the secure setup!

---

## 📝 Quick Reference Card

```bash
# First time setup
python settings_manager.py          # Open GUI, enter keys, save

# Daily usage
python deploy_weather_bot_secure.py  # Run bot with encrypted settings

# Testing
python quick_start.py                # Validate and start

# Management
python settings_manager.py --show    # View current config
python settings_manager.py --export  # Export to environment
```

---

*Your API keys are safe with military-grade encryption. No more hardcoding!*
