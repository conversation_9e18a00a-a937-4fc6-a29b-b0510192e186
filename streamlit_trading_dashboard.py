#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Intuitive Streamlit Dashboard
User-friendly interface for Live, Backtest, and Paper trading modes
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import asyncio
import json
from pathlib import Path
import sqlite3
from typing import Dict, List, Optional
import sys

# Import our unified trading system
from unified_trading_system import UnifiedTradingSystem, DataSource

# Page configuration
st.set_page_config(
    page_title="Weather Trading Bot v2.0",
    page_icon="🌤️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .stButton > button {
        width: 100%;
        background-color: #4CAF50;
        color: white;
        font-weight: bold;
        border-radius: 5px;
        border: none;
        padding: 0.5rem;
        transition: all 0.3s;
    }
    .stButton > button:hover {
        background-color: #45a049;
        transform: translateY(-2px);
    }
    .trading-mode-live {
        background-color: #ff4444;
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-weight: bold;
    }
    .trading-mode-paper {
        background-color: #ffaa00;
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-weight: bold;
    }
    .trading-mode-backtest {
        background-color: #44aaff;
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-weight: bold;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .warning-box {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 10px;
        margin: 10px 0;
    }
    .success-box {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
        padding: 10px;
        margin: 10px 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'trading_system' not in st.session_state:
    st.session_state.trading_system = None
    st.session_state.trading_mode = None
    st.session_state.is_running = False
    st.session_state.performance = {
        'total_trades': 0,
        'win_rate': 0,
        'total_pnl': 0,
        'sharpe_ratio': 0
    }
    st.session_state.positions = []
    st.session_state.recent_trades = []
    st.session_state.backtest_results = None

def get_mode_color(mode):
    """Get color for trading mode indicator"""
    colors = {
        'LIVE': '#ff4444',
        'PAPER': '#ffaa00',
        'BACKTEST': '#44aaff'
    }
    return colors.get(mode, '#888888')

def create_mode_indicator(mode):
    """Create a visual indicator for current trading mode"""
    if mode:
        color = get_mode_color(mode)
        st.markdown(
            f'<div style="background-color: {color}; color: white; padding: 10px; '
            f'border-radius: 5px; text-align: center; font-weight: bold; font-size: 18px;">'
            f'🔴 {mode} MODE ACTIVE</div>',
            unsafe_allow_html=True
        )

def main():
    # Header
    st.title("🌤️ Weather Trading Bot v2.0")
    st.markdown("### Intelligent Weather Derivatives Trading on Kalshi")
    
    # Sidebar - Trading Mode Selection
    with st.sidebar:
        st.header("⚙️ Trading Configuration")
        
        # Mode selector with visual feedback
        st.subheader("📊 Select Trading Mode")
        
        mode_descriptions = {
            "LIVE": "💰 Real Money Trading - Live markets with actual capital",
            "PAPER": "📝 Paper Trading - Live data with simulated execution",
            "BACKTEST": "📈 Backtesting - Historical simulation for strategy testing"
        }
        
        selected_mode = st.radio(
            "Choose your trading mode:",
            options=["LIVE", "PAPER", "BACKTEST"],
            index=1,  # Default to PAPER for safety
            format_func=lambda x: mode_descriptions[x],
            help="Select how you want to run the trading system"
        )
        
        # Mode-specific configuration
        if selected_mode == "LIVE":
            st.warning("⚠️ **LIVE TRADING MODE**\n\nYou will be trading with REAL MONEY. Please ensure you understand the risks.")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.checkbox("I understand the risks", key="risk_acknowledgment"):
                    st.success("✅ Risk acknowledged")
            with col2:
                if st.checkbox("Use production API", key="prod_api"):
                    st.success("✅ Production API")
                    
        elif selected_mode == "BACKTEST":
            st.info("📊 **BACKTESTING MODE**\n\nTest your strategy on historical data.")
            
            # Date range selector for backtesting
            st.subheader("📅 Backtest Period")
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input(
                    "Start Date",
                    value=datetime.now() - timedelta(days=365),
                    max_value=datetime.now().date()
                )
            with col2:
                end_date = st.date_input(
                    "End Date",
                    value=datetime.now().date(),
                    max_value=datetime.now().date()
                )
            
            # Backtest parameters
            st.subheader("💵 Capital & Risk")
            initial_capital = st.number_input(
                "Initial Capital ($)",
                min_value=1000,
                max_value=1000000,
                value=10000,
                step=1000,
                help="Starting capital for backtesting"
            )
            
            position_size = st.slider(
                "Position Size (% of capital)",
                min_value=0.5,
                max_value=5.0,
                value=2.0,
                step=0.5,
                help="Percentage of capital per trade"
            )
            
        elif selected_mode == "PAPER":
            st.success("📝 **PAPER TRADING MODE**\n\nTrade with live data but simulated execution - perfect for testing!")
        
        # Common configuration
        st.subheader("🎯 Trading Parameters")
        
        # Weather markets selection
        weather_markets = st.multiselect(
            "Select Weather Markets",
            options=['NHIGH', 'CHIHIGH', 'MIAHIGH', 'DENHIGH', 'AUSHIGH', 'LAHIGH', 'PHILHIGH', 'RAINNYC'],
            default=['NHIGH', 'CHIHIGH', 'MIAHIGH'],
            help="Choose which weather markets to trade"
        )
        
        # Risk parameters
        with st.expander("⚠️ Risk Management", expanded=False):
            max_positions = st.slider("Max Concurrent Positions", 1, 20, 10)
            stop_loss = st.number_input("Stop Loss ($)", 50, 500, 100, 50)
            take_profit = st.number_input("Take Profit ($)", 100, 1000, 200, 50)
            
        # Advanced settings
        with st.expander("🔧 Advanced Settings", expanded=False):
            confidence_threshold = st.slider("Min Confidence (%)", 50, 90, 70, 5)
            slippage_bps = st.slider("Slippage (basis points)", 0, 50, 10, 5)
            
    # Main content area
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Trading mode indicator
        if st.session_state.trading_mode:
            create_mode_indicator(st.session_state.trading_mode)
            st.markdown("---")
    
    # Control buttons
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🚀 START TRADING", type="primary", disabled=st.session_state.is_running):
            # Initialize trading system
            if selected_mode == "LIVE" and not st.session_state.get('risk_acknowledgment'):
                st.error("⚠️ Please acknowledge the risks before starting live trading!")
            else:
                st.session_state.trading_mode = selected_mode
                st.session_state.is_running = True
                
                # Map mode to DataSource enum
                mode_map = {
                    'LIVE': DataSource.LIVE,
                    'PAPER': DataSource.PAPER,
                    'BACKTEST': DataSource.BACKTEST
                }
                
                # Create trading system instance
                st.session_state.trading_system = UnifiedTradingSystem(mode_map[selected_mode])
                st.success(f"✅ Trading system started in {selected_mode} mode!")
                st.rerun()
    
    with col2:
        if st.button("⏸️ PAUSE", disabled=not st.session_state.is_running):
            st.session_state.is_running = False
            st.warning("⏸️ Trading paused")
    
    with col3:
        if st.button("⏹️ STOP", disabled=not st.session_state.is_running):
            st.session_state.is_running = False
            st.session_state.trading_system = None
            st.session_state.trading_mode = None
            st.info("⏹️ Trading stopped")
            st.rerun()
    
    with col4:
        if st.button("🔄 REFRESH"):
            st.rerun()
    
    # Performance metrics
    st.markdown("---")
    st.header("📊 Performance Metrics")
    
    # Metrics cards
    metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
    
    with metric_col1:
        st.metric(
            "Total Trades",
            st.session_state.performance['total_trades'],
            delta=None if st.session_state.performance['total_trades'] == 0 else f"+{st.session_state.performance['total_trades']}",
            help="Total number of trades executed"
        )
    
    with metric_col2:
        win_rate = st.session_state.performance['win_rate']
        st.metric(
            "Win Rate",
            f"{win_rate:.1%}",
            delta=None if win_rate == 0 else f"{win_rate - 0.5:.1%}",
            delta_color="normal" if win_rate > 0.5 else "inverse",
            help="Percentage of winning trades"
        )
    
    with metric_col3:
        pnl = st.session_state.performance['total_pnl']
        st.metric(
            "Total P&L",
            f"${pnl:,.2f}",
            delta=f"${pnl:,.2f}" if pnl != 0 else None,
            delta_color="normal" if pnl >= 0 else "inverse",
            help="Total profit and loss"
        )
    
    with metric_col4:
        sharpe = st.session_state.performance.get('sharpe_ratio', 0)
        st.metric(
            "Sharpe Ratio",
            f"{sharpe:.2f}",
            delta=None,
            help="Risk-adjusted returns"
        )
    
    # Charts section
    st.markdown("---")
    chart_col1, chart_col2 = st.columns(2)
    
    with chart_col1:
        st.subheader("📈 P&L Over Time")
        
        # Generate sample data if no real data yet
        if st.session_state.recent_trades:
            df = pd.DataFrame(st.session_state.recent_trades)
            fig = px.line(df, x='timestamp', y='cumulative_pnl', 
                         title="Cumulative P&L",
                         labels={'cumulative_pnl': 'P&L ($)', 'timestamp': 'Time'})
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
        else:
            # Sample chart for demonstration
            dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
            pnl = pd.Series(range(100)).cumsum() + pd.Series(range(100)).apply(lambda x: (x % 10 - 5) * 10)
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=dates, y=pnl, mode='lines', name='P&L'))
            fig.update_layout(title="Cumulative P&L (Sample)", height=400)
            st.plotly_chart(fig, use_container_width=True)
    
    with chart_col2:
        st.subheader("🎯 Trade Distribution")
        
        # Win/Loss distribution
        wins = st.session_state.performance.get('winning_trades', 30)
        losses = st.session_state.performance.get('losing_trades', 20)
        
        fig = go.Figure(data=[
            go.Bar(name='Wins', x=['Trades'], y=[wins], marker_color='green'),
            go.Bar(name='Losses', x=['Trades'], y=[losses], marker_color='red')
        ])
        fig.update_layout(title="Win/Loss Distribution", barmode='stack', height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    # Active positions table
    st.markdown("---")
    positions_col, trades_col = st.columns(2)
    
    with positions_col:
        st.subheader("📋 Active Positions")
        
        if st.session_state.positions:
            positions_df = pd.DataFrame(st.session_state.positions)
            st.dataframe(
                positions_df,
                use_container_width=True,
                hide_index=True,
                column_config={
                    "unrealized_pnl": st.column_config.NumberColumn(
                        "Unrealized P&L",
                        format="$%.2f",
                    ),
                    "entry_price": st.column_config.NumberColumn(
                        "Entry Price",
                        format="$%.2f",
                    ),
                }
            )
        else:
            st.info("No active positions")
    
    with trades_col:
        st.subheader("📜 Recent Trades")
        
        if st.session_state.recent_trades:
            trades_df = pd.DataFrame(st.session_state.recent_trades[-10:])  # Last 10 trades
            st.dataframe(
                trades_df,
                use_container_width=True,
                hide_index=True,
                column_config={
                    "pnl": st.column_config.NumberColumn(
                        "P&L",
                        format="$%.2f",
                    ),
                    "timestamp": st.column_config.DatetimeColumn(
                        "Time",
                        format="DD/MM HH:mm",
                    ),
                }
            )
        else:
            st.info("No recent trades")
    
    # Weather data section
    st.markdown("---")
    st.header("🌡️ Live Weather Data")
    
    weather_col1, weather_col2, weather_col3 = st.columns(3)
    
    stations = {
        'KNYC': 'New York',
        'KMDW': 'Chicago',
        'KMIA': 'Miami'
    }
    
    for i, (station, name) in enumerate(stations.items()):
        col = [weather_col1, weather_col2, weather_col3][i]
        with col:
            st.subheader(f"{name} ({station})")
            
            # Sample weather data
            temp = 72 + (i * 5)
            forecast = temp + 5
            confidence = 70 + (i * 5)
            
            st.metric("Current Temp", f"{temp}°F")
            st.metric("Forecast High", f"{forecast}°F", delta=f"+{forecast-temp}°F")
            st.progress(confidence / 100, text=f"Confidence: {confidence}%")
    
    # Log/Status section
    st.markdown("---")
    with st.expander("📝 System Logs", expanded=False):
        st.text_area(
            "Trading Log",
            value="[2024-01-01 09:00:00] System initialized\n"
                  "[2024-01-01 09:00:01] Connected to Kalshi API\n"
                  "[2024-01-01 09:00:02] Weather data feed active\n"
                  "[2024-01-01 09:00:05] Scanning markets...\n"
                  "[2024-01-01 09:00:10] Found 3 opportunities\n"
                  "[2024-01-01 09:00:15] Executing trade NHIGH...",
            height=200,
            disabled=True
        )
    
    # Auto-refresh for live data
    if st.session_state.is_running:
        st.markdown(
            '<div class="success-box">🔄 Auto-refreshing every 60 seconds...</div>',
            unsafe_allow_html=True
        )
        # Auto-refresh would be handled by JavaScript or periodic rerun
    
    # AI Model Information Section
    st.markdown("---")
    st.header("🤖 AI Configuration")
    
    ai_col1, ai_col2, ai_col3 = st.columns(3)
    
    with ai_col1:
        st.info(
            "**AI Model:** xAI Grok-4\n\n"
            "This system uses Grok-4 for all trading decisions and weather analysis."
        )
    
    with ai_col2:
        st.info(
            "**Fallback Model:** Grok-2-1212\n\n"
            "Automatic fallback to Grok-2 if primary model is unavailable."
        )
    
    with ai_col3:
        st.info(
            "**API Required:** xAI API Key\n\n"
            "Get your key from [console.x.ai](https://console.x.ai/)"
        )
    
    # Footer
    st.markdown("---")
    footer_col1, footer_col2, footer_col3, footer_col4 = st.columns(4)
    
    with footer_col1:
        st.markdown("**System Status:** " + ("🟢 Active" if st.session_state.is_running else "🔴 Inactive"))
    
    with footer_col2:
        st.markdown(f"**Trading Mode:** {st.session_state.trading_mode or 'Not Selected'}")
    
    with footer_col3:
        st.markdown(f"**AI Model:** xAI Grok-4")
    
    with footer_col4:
        st.markdown(f"**Last Update:** {datetime.now().strftime('%H:%M:%S')}")

# Async runner for trading system
async def run_trading_system():
    """Run the trading system in the background"""
    if st.session_state.trading_system and st.session_state.is_running:
        await st.session_state.trading_system.run()

if __name__ == "__main__":
    main()
