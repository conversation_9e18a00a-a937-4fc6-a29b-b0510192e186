"""
Kalshi API client for trading operations.
Handles authentication, market data, and trade execution.
"""

import asyncio
import base64
import hashlib
import hmac
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urlencode

import httpx
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding

from src.config.settings import settings
from src.utils.logging_setup import TradingLoggerMixin


class KalshiAPIError(Exception):
    """Custom exception for Kalshi API errors."""
    pass


class KalshiClient(TradingLoggerMixin):
    """
    Kalshi API client for automated trading.
    Handles authentication, market data retrieval, and trade execution.
    """
    
    def __init__(
        self, 
        api_key: Optional[str] = None, 
        private_key_path: str = "kalshi_private_key",
        max_retries: int = 5,
        backoff_factor: float = 0.5
    ):
        """
        Initialize Kalshi client.
        
        Args:
            api_key: Kalshi API key (Key ID from the API key generation)
            private_key_path: Path to private key file
            max_retries: Maximum number of retries for failed requests
            backoff_factor: Factor for exponential backoff
        """
        self.api_key = api_key or settings.api.kalshi_api_key
        self.base_url = settings.api.kalshi_base_url
        self.private_key_path = private_key_path
        self.private_key = None
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        
        # Load private key
        self._load_private_key()
        
        # HTTP client with timeouts
        self.client = httpx.AsyncClient(
            timeout=30.0,
            limits=httpx.Limits(max_keepalive_connections=10, max_connections=20)
        )
        
        self.logger.info("Kalshi client initialized", api_key_length=len(self.api_key) if self.api_key else 0)
    
    def _load_private_key(self) -> None:
        """Load private key from file with security validation."""
        try:
            from ..utils.security_validation import SecurityValidator
            
            # Validate file path for security
            path_result = SecurityValidator.validate_file_path(
                self.private_key_path, 
                allowed_extensions=['pem', 'key']
            )
            if not path_result.is_valid:
                raise KalshiAPIError(f"Invalid private key path: {path_result.error_message}")
            
            private_key_path = Path(path_result.sanitized_value)
            
            # Ensure the path exists and has secure permissions
            if not private_key_path.exists():
                raise KalshiAPIError(f"Private key file not found: {self.private_key_path}")
            
            # Check file permissions (should be 600 or 400 for security)
            import stat
            file_mode = private_key_path.stat().st_mode
            if not (stat.S_IMODE(file_mode) in [0o600, 0o400]):
                self.logger.warning(
                    "Private key file has insecure permissions",
                    file_mode=oct(stat.S_IMODE(file_mode)),
                    recommended="600 or 400"
                )
            
            # Try to get password from environment first
            import os
            password = os.getenv('KALSHI_PRIVATE_KEY_PASSWORD')
            password_bytes = password.encode('utf-8') if password else None
            
            with open(private_key_path, 'rb') as f:
                key_data = f.read()
                
                # Validate key data size (reasonable bounds)
                if len(key_data) < 100 or len(key_data) > 10000:
                    raise KalshiAPIError("Private key file size is suspicious")
                
                try:
                    # First try with password if provided
                    self.private_key = serialization.load_pem_private_key(
                        key_data,
                        password=password_bytes
                    )
                    if password:
                        self.logger.info("Private key loaded successfully with password protection")
                    else:
                        self.logger.warning(
                            "Private key loaded without password protection - consider adding KALSHI_PRIVATE_KEY_PASSWORD"
                        )
                        
                except TypeError:
                    # Key might be password protected but no password provided
                    if not password:
                        self.logger.error("Private key requires password but none provided in KALSHI_PRIVATE_KEY_PASSWORD")
                        raise KalshiAPIError("Private key requires password - set KALSHI_PRIVATE_KEY_PASSWORD environment variable")
                    raise
                    
        except Exception as e:
            self.logger.error("Failed to load private key", error=str(e))
            raise KalshiAPIError(f"Failed to load private key: {e}")
    
    def _sign_request(self, timestamp: str, method: str, path: str) -> str:
        """
        Sign request using RSA PSS signing method as per Kalshi API docs.
        
        Args:
            timestamp: Request timestamp in milliseconds
            method: HTTP method
            path: Request path
        
        Returns:
            Base64 encoded signature
        """
        # Create message to sign: timestamp + method + path
        message = timestamp + method.upper() + path
        message_bytes = message.encode('utf-8')
        
        try:
            # Sign using RSA PSS as per Kalshi documentation
            signature = self.private_key.sign(
                message_bytes,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.DIGEST_LENGTH
                ),
                hashes.SHA256()
            )
            
            return base64.b64encode(signature).decode('utf-8')
        except Exception as e:
            self.logger.error("Failed to sign request", error=str(e))
            raise KalshiAPIError(f"Failed to sign request: {e}")
    
    def _sanitize_error_message(self, status_code: int, response_text: str) -> str:
        """Sanitize error messages to prevent information disclosure."""
        try:
            from ..utils.security_validation import SecurityValidator
            
            # Common safe error messages based on status code
            safe_messages = {
                400: "Bad Request - Invalid parameters",
                401: "Unauthorized - Authentication failed",
                403: "Forbidden - Insufficient permissions",
                404: "Not Found - Resource not found",
                422: "Unprocessable Entity - Validation failed",
                429: "Rate Limited - Too many requests",
                500: "Internal Server Error",
                502: "Bad Gateway",
                503: "Service Unavailable",
                504: "Gateway Timeout"
            }
            
            # Use safe message if available
            if status_code in safe_messages:
                base_message = safe_messages[status_code]
            else:
                base_message = f"HTTP {status_code} Error"
            
            # Try to extract useful but safe information from response
            if response_text:
                # Sanitize response text
                sanitized_result = SecurityValidator.sanitize_text(response_text, max_length=100)
                if sanitized_result.is_valid:
                    # Only include non-sensitive patterns
                    safe_text = sanitized_result.sanitized_value
                    # Remove potential sensitive data patterns
                    import re
                    safe_text = re.sub(r'[a-zA-Z0-9]{20,}', '[REDACTED]', safe_text)  # Remove tokens/keys
                    safe_text = re.sub(r'\b\d{4,}\b', '[NUMBER]', safe_text)  # Remove long numbers
                    
                    if len(safe_text.strip()) > 0 and safe_text != response_text:
                        return f"{base_message}: {safe_text}"
            
            return base_message
            
        except Exception:
            # If sanitization fails, return generic message
            return f"HTTP {status_code} Error"
    
    async def _make_authenticated_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict] = None,
        json_data: Optional[Dict] = None,
        require_auth: bool = True
    ) -> Dict[str, Any]:
        """
        Make authenticated request to Kalshi API with retry logic.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            params: Query parameters
            json_data: JSON request body
            require_auth: Whether authentication is required
        
        Returns:
            API response data
        """
        # Prepare request
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # Add authentication headers if required
        if require_auth:
            # Get current timestamp in milliseconds
            timestamp = str(int(time.time() * 1000))
            
            # Create signature
            signature = self._sign_request(timestamp, method, endpoint)
            
            headers.update({
                "KALSHI-ACCESS-KEY": self.api_key,
                "KALSHI-ACCESS-TIMESTAMP": timestamp,
                "KALSHI-ACCESS-SIGNATURE": signature
            })
        
        # Prepare body
        body = None
        if json_data:
            body = json.dumps(json_data, separators=(',', ':'))
        
        # Add query parameters to URL if present
        if params:
            query_string = urlencode(params)
            url = f"{url}?{query_string}"
        
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(
                    "Making API request",
                    method=method,
                    endpoint=endpoint,
                    has_auth=require_auth,
                    attempt=attempt + 1
                )
                
                # Check rate limiter before making request
                from ..utils.rate_limiter import global_rate_limiter
                if not await global_rate_limiter.acquire("kalshi"):
                    wait_time = await global_rate_limiter.get_wait_time("kalshi")
                    await asyncio.sleep(wait_time)
                    if not await global_rate_limiter.acquire("kalshi"):
                        raise KalshiAPIError("Rate limit exceeded for Kalshi API")
                
                response = await self.client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    content=body if body else None
                )
                
                response.raise_for_status()
                return response.json()
                
            except httpx.HTTPStatusError as e:
                last_exception = e
                # Rate limit (429) or server errors (5xx) are worth retrying
                if e.response.status_code == 429 or e.response.status_code >= 500:
                    sleep_time = self.backoff_factor * (2 ** attempt)
                    self.logger.warning(
                        f"API request failed with status {e.response.status_code}. Retrying in {sleep_time:.2f}s...",
                        endpoint=endpoint,
                        attempt=attempt + 1
                    )
                    await asyncio.sleep(sleep_time)
                else:
                    # Don't retry on other client errors (e.g., 400, 401, 404)
                    # Sanitize error message to prevent information disclosure
                    safe_error_msg = self._sanitize_error_message(e.response.status_code, e.response.text)
                    self.logger.error("API request failed without retry", error=safe_error_msg, endpoint=endpoint)
                    raise KalshiAPIError(safe_error_msg)
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Request failed with general exception. Retrying...", error=str(e), endpoint=endpoint)
                sleep_time = self.backoff_factor * (2 ** attempt)
                await asyncio.sleep(sleep_time)
        
        raise KalshiAPIError(f"API request failed after {self.max_retries} retries: {last_exception}")
    
    async def get_balance(self) -> Dict[str, Any]:
        """Get account balance."""
        return await self._make_authenticated_request("GET", "/trade-api/v2/portfolio/balance")
    
    async def get_positions(self, ticker: Optional[str] = None) -> Dict[str, Any]:
        """Get portfolio positions."""
        params = {}
        if ticker:
            params["ticker"] = ticker
        return await self._make_authenticated_request("GET", "/trade-api/v2/portfolio/positions", params=params)
    
    async def get_fills(self, ticker: Optional[str] = None, limit: int = 100) -> Dict[str, Any]:
        """Get order fills.""" 
        params = {"limit": limit}
        if ticker:
            params["ticker"] = ticker
        return await self._make_authenticated_request("GET", "/trade-api/v2/portfolio/fills", params=params)
    
    async def get_orders(self, ticker: Optional[str] = None, status: Optional[str] = None) -> Dict[str, Any]:
        """Get orders."""
        params = {}
        if ticker:
            params["ticker"] = ticker
        if status:
            params["status"] = status
        return await self._make_authenticated_request("GET", "/trade-api/v2/portfolio/orders", params=params)
    
    async def get_markets(
        self,
        limit: int = 100,
        cursor: Optional[str] = None,
        event_ticker: Optional[str] = None,
        series_ticker: Optional[str] = None,
        status: Optional[str] = None,
        tickers: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Get markets data.
        
        Args:
            limit: Maximum number of markets to return
            cursor: Pagination cursor
            event_ticker: Filter by event ticker
            series_ticker: Filter by series ticker
            status: Filter by market status
            tickers: List of specific tickers to fetch
        
        Returns:
            Markets data
        """
        params = {"limit": limit}
        
        if cursor:
            params["cursor"] = cursor
        if event_ticker:
            params["event_ticker"] = event_ticker
        if series_ticker:
            params["series_ticker"] = series_ticker
        if status:
            params["status"] = status
        if tickers:
            params["tickers"] = ",".join(tickers)
        
        return await self._make_authenticated_request(
            "GET", "/trade-api/v2/markets", params=params, require_auth=True
        )
    
    async def get_market(self, ticker: str) -> Dict[str, Any]:
        """Get specific market data."""
        return await self._make_authenticated_request(
            "GET", f"/trade-api/v2/markets/{ticker}", require_auth=False
        )
    
    async def get_orderbook(self, ticker: str, depth: int = 100) -> Dict[str, Any]:
        """
        Get market orderbook.
        
        Args:
            ticker: Market ticker
            depth: Orderbook depth
        
        Returns:
            Orderbook data
        """
        params = {"depth": depth}
        return await self._make_authenticated_request(
            "GET", f"/trade-api/v2/markets/{ticker}/orderbook", params=params, require_auth=False
        )
    
    async def get_market_history(
        self,
        ticker: str,
        start_ts: Optional[int] = None,
        end_ts: Optional[int] = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        Get market price history.
        
        Args:
            ticker: Market ticker
            start_ts: Start timestamp
            end_ts: End timestamp
            limit: Number of records to return
        
        Returns:
            Price history data
        """
        params = {"limit": limit}
        if start_ts:
            params["start_ts"] = start_ts
        if end_ts:
            params["end_ts"] = end_ts
        
        return await self._make_authenticated_request(
            "GET", f"/trade-api/v2/markets/{ticker}/history", params=params, require_auth=False
        )
    
    async def place_order(
        self,
        ticker: str,
        client_order_id: str,
        side: str,
        action: str,
        count: int,
        type_: str = "market",
        yes_price: Optional[int] = None,
        no_price: Optional[int] = None,
        expiration_ts: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Place a trading order with comprehensive input validation.
        
        Args:
            ticker: Market ticker
            client_order_id: Unique client order ID
            side: "yes" or "no"
            action: "buy" or "sell"
            count: Number of contracts
            type_: Order type ("market" or "limit")
            yes_price: Yes price in cents (for limit orders)
            no_price: No price in cents (for limit orders)
            expiration_ts: Order expiration timestamp
        
        Returns:
            Order response
            
        Raises:
            KalshiAPIError: If input validation fails
        """
        from ..utils.security_validation import SecurityValidator
        
        try:
            # Validate ticker
            ticker_result = SecurityValidator.validate_market_id(ticker)
            if not ticker_result.is_valid:
                raise KalshiAPIError(f"Invalid ticker: {ticker_result.error_message}")
            
            # Validate client_order_id (should be UUID or similar)
            if not isinstance(client_order_id, str) or len(client_order_id) < 10 or len(client_order_id) > 50:
                raise KalshiAPIError("Invalid client_order_id format")
            
            # Validate side
            if side.lower() not in ['yes', 'no']:
                raise KalshiAPIError("Side must be 'yes' or 'no'")
            
            # Validate action
            if action.lower() not in ['buy', 'sell']:
                raise KalshiAPIError("Action must be 'buy' or 'sell'")
            
            # Validate count
            count_result = SecurityValidator.validate_quantity(count)
            if not count_result.is_valid:
                raise KalshiAPIError(f"Invalid count: {count_result.error_message}")
            
            # Validate type
            if type_ not in ['market', 'limit']:
                raise KalshiAPIError("Type must be 'market' or 'limit'")
            
            # Validate prices (in cents, 1-99)
            if yes_price is not None:
                if not isinstance(yes_price, int) or not (1 <= yes_price <= 99):
                    raise KalshiAPIError("Yes price must be between 1 and 99 cents")
            
            if no_price is not None:
                if not isinstance(no_price, int) or not (1 <= no_price <= 99):
                    raise KalshiAPIError("No price must be between 1 and 99 cents")
            
            # Validate expiration timestamp
            if expiration_ts is not None:
                if not isinstance(expiration_ts, int) or expiration_ts < 0:
                    raise KalshiAPIError("Invalid expiration timestamp")
                
                # Check if expiration is reasonable (not too far in future)
                import time
                max_expiry = int(time.time()) + (365 * 24 * 60 * 60)  # 1 year max
                if expiration_ts > max_expiry:
                    raise KalshiAPIError("Expiration timestamp too far in future")
            
            # Limit order validation
            if type_ == "limit":
                if yes_price is None and no_price is None:
                    raise KalshiAPIError("Limit orders require either yes_price or no_price")
                if yes_price is not None and no_price is not None:
                    raise KalshiAPIError("Limit orders should specify only one price (yes or no)")
            
            # Build validated order data
            order_data = {
                "ticker": ticker_result.sanitized_value,
                "client_order_id": client_order_id,
                "side": side.lower(),
                "action": action.lower(),
                "count": count_result.sanitized_value,
                "type": type_
            }
            
            if yes_price is not None:
                order_data["yes_price"] = yes_price
            if no_price is not None:
                order_data["no_price"] = no_price
            if expiration_ts:
                order_data["expiration_ts"] = expiration_ts
            
            self.logger.info(
                "Placing validated order",
                ticker=ticker_result.sanitized_value,
                side=side.lower(),
                action=action.lower(),
                count=count_result.sanitized_value,
                type=type_
            )
            
            return await self._make_authenticated_request(
                "POST", "/trade-api/v2/portfolio/orders", json_data=order_data
            )
            
        except Exception as e:
            self.logger.error(f"Order validation failed: {e}")
            if isinstance(e, KalshiAPIError):
                raise
            else:
                raise KalshiAPIError(f"Order placement failed: {e}")
    
    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order."""
        return await self._make_authenticated_request(
            "DELETE", f"/trade-api/v2/portfolio/orders/{order_id}"
        )
    
    async def get_trades(
        self,
        ticker: Optional[str] = None,
        limit: int = 100,
        cursor: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get trade history.
        
        Args:
            ticker: Filter by ticker
            limit: Maximum number of trades to return
            cursor: Pagination cursor
        
        Returns:
            Trades data
        """
        params = {"limit": limit}
        if ticker:
            params["ticker"] = ticker
        if cursor:
            params["cursor"] = cursor
        
        return await self._make_authenticated_request(
            "GET", "/trade-api/v2/portfolio/trades", params=params
        )
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self.client.aclose()
        self.logger.info("Kalshi client closed")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close() 