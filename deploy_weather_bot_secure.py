#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Secure Deployment Script
This version loads settings from the secure settings manager.
"""

import os
import sys
import asyncio
import argparse
import signal
import time
from datetime import datetime
from pathlib import Path

# Import the secure settings manager
from settings_manager import SecureSettingsManager

# Add kalshi-ai-trading-bot to path
sys.path.insert(0, 'kalshi-ai-trading-bot')
sys.path.insert(0, 'kalshi-ai-trading-bot/src')

# Load and export settings
manager = SecureSettingsManager()
settings = manager.load_settings()

if not settings:
    print("\n❌ No settings found!")
    print("Please run 'python settings_manager.py' to configure your API keys first.\n")
    sys.exit(1)

# Export settings to environment
manager.export_to_env(settings)

# Now import the bot components
from src.weather.noaa_client import NOAAClient
from src.weather.weather_analyzer import WeatherAnalyzer
from src.strategies.weather_trading import WeatherTradingStrategy
from src.agents.kalshi_client import KalshiClient
from src.agents.xai_client import XAIClient

try:
    from weather_dashboard_extension import WeatherDashboard
except ImportError:
    WeatherDashboard = None

class WeatherBotDeployment:
    """Production deployment manager for Weather Trading Bot."""
    
    def __init__(self):
        self.running = False
        self.settings = settings
        
    async def validate_environment(self):
        """Validate environment and settings."""
        print("🔍 Validating environment...")
        
        # Check required API keys
        required_keys = ['KALSHI_API_KEY', 'KALSHI_API_SECRET']
        llm_keys = ['XAI_API_KEY', 'OPENAI_API_KEY']
        
        missing = []
        for key in required_keys:
            if not self.settings.get(key):
                missing.append(key)
        
        # Check for at least one LLM key
        has_llm = any(self.settings.get(key) for key in llm_keys)
        if not has_llm:
            missing.append("LLM API Key (XAI or OpenAI)")
        
        if missing:
            print(f"❌ Missing required settings: {', '.join(missing)}")
            print("\nPlease run 'python settings_manager.py' to configure.")
            return False
        
        print("✅ Environment validated")
        
        # Display configuration
        print("\n📋 Configuration:")
        print(f"  • Kalshi Environment: {self.settings.get('KALSHI_ENV', 'demo')}")
        print(f"  • Trading Mode: {self.settings.get('TRADING_MODE', 'paper')}")
        print(f"  • Max Position: ${self.settings.get('MAX_POSITION_SIZE', '25000')}")
        print(f"  • Daily Loss Limit: {self.settings.get('DAILY_LOSS_LIMIT', '5')}%")
        print(f"  • Update Frequency: {self.settings.get('UPDATE_FREQUENCY', '15')} minutes")
        
        return True
    
    async def initialize_components(self):
        """Initialize all bot components."""
        print("\n🚀 Initializing components...")
        
        try:
            # Initialize NOAA client
            print("  • Initializing NOAA client...")
            self.noaa_client = NOAAClient()
            
            # Initialize Kalshi client
            print("  • Initializing Kalshi client...")
            self.kalshi_client = KalshiClient()
            
            # Initialize LLM client
            print("  • Initializing LLM client...")
            provider = self.settings.get('LLM_PROVIDER', 'xai')
            if provider == 'xai':
                self.llm_client = XAIClient()
            else:
                # Would need to import appropriate client
                self.llm_client = XAIClient()  # Default to XAI
            
            # Initialize weather analyzer
            print("  • Initializing weather analyzer...")
            self.weather_analyzer = WeatherAnalyzer(
                llm_client=self.llm_client,
                enable_enhanced=self.settings.get('ENABLE_ENHANCED', 'True') == 'True'
            )
            
            # Initialize trading strategy
            print("  • Initializing trading strategy...")
            self.trading_strategy = WeatherTradingStrategy(
                kalshi_client=self.kalshi_client,
                weather_analyzer=self.weather_analyzer,
                noaa_client=self.noaa_client
            )
            
            # Initialize dashboard if available
            if WeatherDashboard:
                print("  • Initializing dashboard...")
                port = int(self.settings.get('DASHBOARD_PORT', '8080'))
                self.dashboard = WeatherDashboard(port=port)
                await self.dashboard.start()
                print(f"  ✅ Dashboard running at http://localhost:{port}")
            
            print("✅ All components initialized")
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    async def run_trading_cycle(self):
        """Run a single trading cycle."""
        print(f"\n⚡ Running trading cycle at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # Get weather data
            print("  • Fetching weather data...")
            weather_data = await self.noaa_client.get_all_station_observations()
            
            # Analyze opportunities
            print("  • Analyzing trading opportunities...")
            opportunities = await self.trading_strategy.identify_opportunities()
            
            if opportunities:
                print(f"  • Found {len(opportunities)} opportunities")
                
                # Execute trades based on mode
                mode = self.settings.get('TRADING_MODE', 'paper')
                if mode == 'paper':
                    print("  • Paper trading mode - simulating trades")
                elif mode == 'production':
                    print("  • Production mode - executing real trades")
                    await self.trading_strategy.execute_trades(opportunities)
            else:
                print("  • No opportunities found this cycle")
            
            # Update dashboard if available
            if hasattr(self, 'dashboard'):
                await self.dashboard.update(weather_data, opportunities)
            
            print("✅ Trading cycle complete")
            
        except Exception as e:
            print(f"❌ Trading cycle error: {e}")
    
    async def run_production(self, interval_minutes=15):
        """Run continuous production trading."""
        self.running = True
        
        print(f"\n🎯 Starting production trading (interval: {interval_minutes} minutes)")
        print("Press Ctrl+C to stop gracefully\n")
        
        while self.running:
            try:
                # Run trading cycle
                await self.run_trading_cycle()
                
                # Wait for next cycle
                print(f"\n⏰ Next cycle in {interval_minutes} minutes...")
                await asyncio.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                print("\n⚠️  Shutdown requested...")
                break
            except Exception as e:
                print(f"❌ Error in production loop: {e}")
                print("Retrying in 60 seconds...")
                await asyncio.sleep(60)
    
    def stop(self):
        """Stop the bot gracefully."""
        print("\n🛑 Stopping Weather Trading Bot...")
        self.running = False
        
        if hasattr(self, 'dashboard'):
            asyncio.create_task(self.dashboard.stop())
        
        print("✅ Bot stopped successfully")

async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Weather Trading Bot Deployment')
    parser.add_argument('--mode', choices=['test', 'paper', 'production'], 
                       default='paper', help='Trading mode')
    parser.add_argument('--interval', type=int, default=15,
                       help='Update interval in minutes')
    
    args = parser.parse_args()
    
    # Override mode from settings if not specified
    if args.mode == 'paper' and 'TRADING_MODE' in settings:
        args.mode = settings['TRADING_MODE']
    
    print("\n" + "="*60)
    print("   🌡️  Weather Trading Bot v2.0 - Secure Deployment")
    print("="*60)
    
    bot = WeatherBotDeployment()
    
    # Validate environment
    if not await bot.validate_environment():
        return
    
    # Initialize components
    if not await bot.initialize_components():
        return
    
    # Handle shutdown signals
    def signal_handler(sig, frame):
        bot.stop()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Run based on mode
    if args.mode == 'test':
        print("\n🧪 Running single test cycle...")
        await bot.run_trading_cycle()
        print("\n✅ Test complete")
    else:
        interval = int(settings.get('UPDATE_FREQUENCY', args.interval))
        await bot.run_production(interval)

if __name__ == "__main__":
    asyncio.run(main())
