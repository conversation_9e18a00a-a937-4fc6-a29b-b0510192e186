#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Automated Installation Script

This script automates the installation of all required packages for the
Weather Trading Bot, with options for different installation profiles.
"""

import sys
import subprocess
import os
import platform
import argparse
from pathlib import Path


class Colors:
    """Terminal colors for pretty output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def print_header(text):
    """Print a formatted header."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{text.center(60)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}\n")


def print_success(text):
    """Print success message."""
    print(f"{Colors.OKGREEN}✅ {text}{Colors.ENDC}")


def print_error(text):
    """Print error message."""
    print(f"{Colors.FAIL}❌ {text}{Colors.ENDC}")


def print_warning(text):
    """Print warning message."""
    print(f"{Colors.WARNING}⚠️  {text}{Colors.ENDC}")


def print_info(text):
    """Print info message."""
    print(f"{Colors.OKBLUE}ℹ️  {text}{Colors.ENDC}")


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print_error(f"Python 3.9+ required. You have {version.major}.{version.minor}.{version.micro}")
        return False
    print_success(f"Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def check_pip():
    """Check if pip is installed."""
    try:
        subprocess.run(["pip", "--version"], capture_output=True, check=True)
        print_success("pip is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_error("pip is not installed")
        return False


def create_virtual_env():
    """Create a virtual environment if it doesn't exist."""
    venv_path = Path("venv")
    if venv_path.exists():
        print_info("Virtual environment already exists")
        return True
    
    print_info("Creating virtual environment...")
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print_success("Virtual environment created")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to create virtual environment: {e}")
        return False


def get_pip_command():
    """Get the appropriate pip command for the current environment."""
    if os.name == 'nt':  # Windows
        if Path("venv/Scripts/pip.exe").exists():
            return ["venv\\Scripts\\pip.exe"]
    else:  # Unix-like
        if Path("venv/bin/pip").exists():
            return ["venv/bin/pip"]
    return ["pip"]


def upgrade_pip():
    """Upgrade pip to the latest version."""
    print_info("Upgrading pip...")
    pip_cmd = get_pip_command()
    try:
        subprocess.run(pip_cmd + ["install", "--upgrade", "pip"], check=True)
        print_success("pip upgraded successfully")
        return True
    except subprocess.CalledProcessError as e:
        print_warning(f"Failed to upgrade pip: {e}")
        return False


def install_core_packages():
    """Install core required packages."""
    print_header("Installing Core Packages")
    
    core_packages = [
        "httpx",
        "aiohttp",
        "requests",
        "pandas",
        "numpy",
        "scipy",
        "python-dotenv",
        "pydantic",
        "cryptography"
    ]
    
    pip_cmd = get_pip_command()
    failed = []
    
    for package in core_packages:
        print_info(f"Installing {package}...")
        try:
            subprocess.run(pip_cmd + ["install", package], 
                         capture_output=True, check=True)
            print_success(f"{package} installed")
        except subprocess.CalledProcessError:
            print_error(f"Failed to install {package}")
            failed.append(package)
    
    if failed:
        print_warning(f"Failed packages: {', '.join(failed)}")
        return False
    return True


def install_weather_packages():
    """Install weather-specific packages."""
    print_header("Installing Weather Packages")
    
    weather_packages = [
        "python-metar",
        "siphon",
        "metpy",
        "noaa-sdk",
        "pynws",
        "xarray",
        "netCDF4",
        "diskcache"
    ]
    
    pip_cmd = get_pip_command()
    failed = []
    
    for package in weather_packages:
        print_info(f"Installing {package}...")
        try:
            subprocess.run(pip_cmd + ["install", package], 
                         capture_output=True, check=True)
            print_success(f"{package} installed")
        except subprocess.CalledProcessError:
            print_error(f"Failed to install {package}")
            failed.append(package)
    
    if failed:
        print_warning(f"Failed packages: {', '.join(failed)}")
        # Weather packages are critical
        return len(failed) < 3  # Allow up to 2 failures
    return True


def install_from_requirements(profile="full"):
    """Install packages from requirements.txt."""
    print_header(f"Installing from requirements.txt ({profile} profile)")
    
    req_file = Path("kalshi-ai-trading-bot/requirements.txt")
    if not req_file.exists():
        print_error(f"requirements.txt not found at {req_file}")
        return False
    
    pip_cmd = get_pip_command()
    
    try:
        if profile == "full":
            subprocess.run(pip_cmd + ["install", "-r", str(req_file)], check=True)
        else:
            # For minimal installation, install only essential packages
            subprocess.run(pip_cmd + ["install", "-r", str(req_file), 
                         "--no-deps"], check=False)
            # Then install core dependencies
            install_core_packages()
            install_weather_packages()
        
        print_success("Packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to install packages: {e}")
        return False


def test_installation():
    """Test if key packages are installed correctly."""
    print_header("Testing Installation")
    
    test_imports = [
        ("pandas", "Data analysis"),
        ("numpy", "Numerical computing"),
        ("scipy", "Scientific computing"),
        ("metar.Metar", "METAR parsing"),
        ("siphon", "NOAA data access"),
        ("metpy", "Weather calculations"),
        ("httpx", "HTTP client"),
        ("pydantic", "Data validation")
    ]
    
    failed = []
    for module_name, description in test_imports:
        try:
            if "." in module_name:
                parts = module_name.split(".")
                module = __import__(parts[0])
                for part in parts[1:]:
                    module = getattr(module, part)
            else:
                __import__(module_name)
            print_success(f"{description} ({module_name})")
        except ImportError:
            print_error(f"{description} ({module_name})")
            failed.append(module_name)
    
    if failed:
        print_warning(f"\nFailed imports: {', '.join(failed)}")
        print_info("Try installing missing packages individually")
        return False
    
    print_success("\nAll core packages are working!")
    return True


def create_env_file():
    """Create a template .env file if it doesn't exist."""
    env_file = Path(".env")
    if env_file.exists():
        print_info(".env file already exists")
        return
    
    print_info("Creating .env template...")
    
    env_template = """# Weather Trading Bot v2.0 - Environment Variables

# === API Keys ===
KALSHI_API_KEY=your_kalshi_api_key_here
KALSHI_API_SECRET=your_kalshi_api_secret_here
XAI_API_KEY=your_xai_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# === Environment Settings ===
KALSHI_ENV=sandbox  # Change to 'production' when ready
LOG_LEVEL=INFO
DEBUG_MODE=False

# === Optional Services ===
# SENTRY_DSN=your_sentry_dsn_here
# REDIS_URL=redis://localhost:6379
# DATABASE_URL=sqlite:///trading_bot.db

# === Weather Settings ===
WEATHER_UPDATE_INTERVAL=900  # 15 minutes in seconds
MAX_WEATHER_POSITIONS=5
WEATHER_CONFIDENCE_THRESHOLD=0.6

# === Trading Settings ===
MAX_POSITION_SIZE=25000  # Kalshi limit
DAILY_LOSS_LIMIT=0.05  # 5% of capital
PORTFOLIO_WEATHER_EXPOSURE=0.4  # 40% max in weather
"""
    
    env_file.write_text(env_template)
    print_success(".env template created - Please add your API keys")


def main():
    """Main installation function."""
    parser = argparse.ArgumentParser(
        description="Weather Trading Bot v2.0 - Installation Script"
    )
    parser.add_argument(
        "--profile",
        choices=["minimal", "core", "full"],
        default="full",
        help="Installation profile (minimal: essential only, core: trading bot, full: everything)"
    )
    parser.add_argument(
        "--skip-venv",
        action="store_true",
        help="Skip virtual environment creation"
    )
    parser.add_argument(
        "--test-only",
        action="store_true",
        help="Only test the installation"
    )
    
    args = parser.parse_args()
    
    print_header("Weather Trading Bot v2.0 - Installation")
    print_info(f"Platform: {platform.system()} {platform.release()}")
    print_info(f"Profile: {args.profile}")
    
    # Check prerequisites
    if not check_python_version():
        return 1
    
    if not check_pip():
        return 1
    
    if args.test_only:
        test_installation()
        return 0
    
    # Create virtual environment
    if not args.skip_venv:
        if not create_virtual_env():
            print_warning("Continuing without virtual environment...")
    
    # Upgrade pip
    upgrade_pip()
    
    # Install packages based on profile
    success = False
    if args.profile == "minimal":
        success = install_core_packages() and install_weather_packages()
    elif args.profile == "core":
        success = install_core_packages() and install_weather_packages()
    else:  # full
        success = install_from_requirements("full")
    
    if not success:
        print_error("\nInstallation completed with errors")
        print_info("Try installing failed packages manually")
    else:
        print_success("\nInstallation completed successfully!")
    
    # Test installation
    print("\n")
    test_installation()
    
    # Create .env template
    create_env_file()
    
    # Print next steps
    print_header("Next Steps")
    print("1. Add your API keys to the .env file")
    print("2. Run: python test_weather_data.py")
    print("3. Run: python settings_manager.py (for GUI setup)")
    print("4. Run: python deploy_weather_bot.py --mode test")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
