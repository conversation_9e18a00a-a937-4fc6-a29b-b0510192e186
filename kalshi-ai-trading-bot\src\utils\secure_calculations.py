"""
Secure financial calculations using Decimal arithmetic.
Prevents overflow, underflow, and precision errors in trading calculations.
"""

from decimal import Decimal, ROUND_HALF_UP, InvalidOperation, getcontext
from typing import Union, Optional
import structlog

# Set high precision for financial calculations
getcontext().prec = 28

logger = structlog.get_logger(__name__)

# Financial constants
MIN_PRICE = Decimal('0.01')
MAX_PRICE = Decimal('0.99')
MIN_QUANTITY = 1
MAX_QUANTITY = 10000
MIN_BALANCE = Decimal('1.00')
MAX_BALANCE = Decimal('1000000.00')  # 1M USD
MAX_POSITION_VALUE = Decimal('100000.00')  # 100K USD per position


class SecureFinancialCalculator:
    """Secure financial calculations with overflow protection."""
    
    @staticmethod
    def safe_decimal_conversion(value: Union[str, float, int, Decimal], name: str = "value") -> Decimal:
        """Safely convert various types to Decimal with validation."""
        try:
            if isinstance(value, Decimal):
                return value
            elif isinstance(value, (int, float)):
                if not (-1e10 <= value <= 1e10):  # Reasonable bounds check
                    raise ValueError(f"{name} is outside reasonable bounds: {value}")
                return Decimal(str(value))
            elif isinstance(value, str):
                # Clean the string of any non-numeric characters except decimal point and minus
                import re
                clean_value = re.sub(r'[^\d.-]', '', value)
                if not clean_value:
                    raise ValueError(f"Empty {name} after cleaning")
                return Decimal(clean_value)
            else:
                raise TypeError(f"{name} must be numeric, got {type(value)}")
        except (InvalidOperation, ValueError) as e:
            logger.error(f"Failed to convert {name} to Decimal", value=value, error=str(e))
            raise ValueError(f"Invalid {name}: {value}")

    @staticmethod
    def calculate_position_quantity(
        balance: Union[str, float, int, Decimal],
        market_price: Union[str, float, int, Decimal],
        position_size_pct: Union[str, float, int, Decimal],
        max_position_pct: Union[str, float, int, Decimal] = Decimal('0.05'),
        confidence_multiplier: Union[str, float, int, Decimal] = Decimal('1.0')
    ) -> int:
        """
        Calculate position quantity with secure arithmetic and bounds checking.
        
        Args:
            balance: Account balance
            market_price: Current market price (0.01 to 0.99)
            position_size_pct: Base position size as percentage (0.01 to 0.20)
            max_position_pct: Maximum position size as percentage
            confidence_multiplier: Multiplier based on confidence (0.1 to 2.0)
            
        Returns:
            Safe position quantity (integer)
        """
        try:
            # Convert all inputs to Decimal for precision
            balance_dec = SecureFinancialCalculator.safe_decimal_conversion(balance, "balance")
            market_price_dec = SecureFinancialCalculator.safe_decimal_conversion(market_price, "market_price")
            position_size_pct_dec = SecureFinancialCalculator.safe_decimal_conversion(position_size_pct, "position_size_pct")
            max_position_pct_dec = SecureFinancialCalculator.safe_decimal_conversion(max_position_pct, "max_position_pct")
            confidence_mult_dec = SecureFinancialCalculator.safe_decimal_conversion(confidence_multiplier, "confidence_multiplier")
            
            # Validate inputs
            if balance_dec < MIN_BALANCE:
                logger.warning(f"Balance too low for trading: ${balance_dec}")
                return 0
            
            if balance_dec > MAX_BALANCE:
                logger.error(f"Balance exceeds maximum: ${balance_dec}")
                raise ValueError("Balance exceeds maximum allowed")
            
            if not (MIN_PRICE <= market_price_dec <= MAX_PRICE):
                logger.error(f"Market price out of range: {market_price_dec}")
                return 0
            
            if not (Decimal('0.001') <= position_size_pct_dec <= Decimal('0.20')):
                logger.error(f"Position size percentage out of range: {position_size_pct_dec}")
                return 0
            
            if not (Decimal('0.1') <= confidence_mult_dec <= Decimal('2.0')):
                logger.warning(f"Confidence multiplier capped: {confidence_mult_dec}")
                confidence_mult_dec = max(Decimal('0.1'), min(Decimal('2.0'), confidence_mult_dec))
            
            # Calculate base investment amount
            base_investment = balance_dec * position_size_pct_dec
            
            # Apply confidence multiplier
            adjusted_investment = base_investment * confidence_mult_dec
            
            # Apply maximum position limit
            max_investment = balance_dec * max_position_pct_dec
            final_investment = min(adjusted_investment, max_investment)
            
            # Calculate quantity (round down for safety)
            quantity_decimal = final_investment / market_price_dec
            quantity = int(quantity_decimal)
            
            # Apply hard limits for safety
            quantity = max(0, min(quantity, MAX_QUANTITY))
            
            # Final position value check
            position_value = market_price_dec * Decimal(str(quantity))
            if position_value > MAX_POSITION_VALUE:
                logger.warning(f"Position value too large, reducing quantity")
                quantity = int(MAX_POSITION_VALUE / market_price_dec)
            
            logger.info(
                "Position quantity calculated",
                balance=float(balance_dec),
                market_price=float(market_price_dec),
                final_investment=float(final_investment),
                quantity=quantity,
                position_value=float(position_value)
            )
            
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating position quantity: {e}")
            return 0

    @staticmethod
    def calculate_kelly_position_size(
        balance: Union[str, float, int, Decimal],
        win_probability: Union[str, float, int, Decimal],
        win_odds: Union[str, float, int, Decimal],
        kelly_fraction: Union[str, float, int, Decimal] = Decimal('0.25')
    ) -> Decimal:
        """
        Calculate Kelly Criterion position size with safety limits.
        
        Args:
            balance: Account balance
            win_probability: Probability of winning (0.0 to 1.0)
            win_odds: Odds of winning (e.g., 2.0 for 2:1 odds)
            kelly_fraction: Fraction of Kelly to use (default 0.25 for quarter Kelly)
            
        Returns:
            Position size in dollars
        """
        try:
            # Convert inputs
            balance_dec = SecureFinancialCalculator.safe_decimal_conversion(balance, "balance")
            win_prob_dec = SecureFinancialCalculator.safe_decimal_conversion(win_probability, "win_probability")
            win_odds_dec = SecureFinancialCalculator.safe_decimal_conversion(win_odds, "win_odds")
            kelly_frac_dec = SecureFinancialCalculator.safe_decimal_conversion(kelly_fraction, "kelly_fraction")
            
            # Validate inputs
            if not (Decimal('0.01') <= win_prob_dec <= Decimal('0.99')):
                logger.warning(f"Win probability out of range: {win_prob_dec}")
                return Decimal('0')
            
            if not (Decimal('1.01') <= win_odds_dec <= Decimal('10.0')):
                logger.warning(f"Win odds out of range: {win_odds_dec}")
                return Decimal('0')
            
            if not (Decimal('0.01') <= kelly_frac_dec <= Decimal('1.0')):
                logger.warning(f"Kelly fraction out of range: {kelly_frac_dec}")
                kelly_frac_dec = Decimal('0.25')  # Safe default
            
            # Kelly formula: f = (bp - q) / b
            # Where: b = odds - 1, p = win probability, q = loss probability
            b = win_odds_dec - Decimal('1')
            p = win_prob_dec
            q = Decimal('1') - win_prob_dec
            
            if b <= 0:
                logger.warning("Invalid odds for Kelly calculation")
                return Decimal('0')
            
            # Calculate Kelly percentage
            kelly_percentage = (b * p - q) / b
            
            # Apply fractional Kelly for safety
            fractional_kelly = kelly_percentage * kelly_frac_dec
            
            # Ensure non-negative
            if fractional_kelly <= 0:
                return Decimal('0')
            
            # Calculate position size
            position_size = balance_dec * fractional_kelly
            
            # Apply maximum position limit (5% of balance)
            max_position = balance_dec * Decimal('0.05')
            position_size = min(position_size, max_position)
            
            # Round to cents
            position_size = position_size.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
            logger.info(
                "Kelly position size calculated",
                kelly_percentage=float(kelly_percentage),
                fractional_kelly=float(fractional_kelly),
                position_size=float(position_size)
            )
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating Kelly position size: {e}")
            return Decimal('0')

    @staticmethod
    def calculate_price_in_cents(price_dollars: Union[str, float, int, Decimal]) -> int:
        """Convert price from dollars to cents with validation."""
        try:
            price_dec = SecureFinancialCalculator.safe_decimal_conversion(price_dollars, "price")
            
            if not (MIN_PRICE <= price_dec <= MAX_PRICE):
                raise ValueError(f"Price must be between ${MIN_PRICE} and ${MAX_PRICE}")
            
            # Convert to cents and round
            cents = price_dec * Decimal('100')
            cents_rounded = int(cents.quantize(Decimal('1'), rounding=ROUND_HALF_UP))
            
            return max(1, min(99, cents_rounded))  # Ensure 1-99 cents
            
        except Exception as e:
            logger.error(f"Error converting price to cents: {e}")
            return 50  # Safe default (50 cents)

    @staticmethod
    def calculate_position_value(
        price: Union[str, float, int, Decimal],
        quantity: int
    ) -> Decimal:
        """Calculate total position value with validation."""
        try:
            price_dec = SecureFinancialCalculator.safe_decimal_conversion(price, "price")
            
            if not (MIN_PRICE <= price_dec <= MAX_PRICE):
                raise ValueError(f"Price must be between ${MIN_PRICE} and ${MAX_PRICE}")
            
            if not (1 <= quantity <= MAX_QUANTITY):
                raise ValueError(f"Quantity must be between 1 and {MAX_QUANTITY}")
            
            value = price_dec * Decimal(str(quantity))
            
            if value > MAX_POSITION_VALUE:
                logger.warning(f"Position value exceeds maximum: ${value}")
                return MAX_POSITION_VALUE
            
            return value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
        except Exception as e:
            logger.error(f"Error calculating position value: {e}")
            return Decimal('0')

    @staticmethod
    def calculate_profit_loss(
        entry_price: Union[str, float, int, Decimal],
        exit_price: Union[str, float, int, Decimal],
        quantity: int,
        side: str
    ) -> Decimal:
        """Calculate profit/loss for a position with validation."""
        try:
            entry_dec = SecureFinancialCalculator.safe_decimal_conversion(entry_price, "entry_price")
            exit_dec = SecureFinancialCalculator.safe_decimal_conversion(exit_price, "exit_price")
            
            if not (MIN_PRICE <= entry_dec <= MAX_PRICE):
                raise ValueError(f"Entry price out of range: {entry_dec}")
            
            if not (MIN_PRICE <= exit_dec <= MAX_PRICE):
                raise ValueError(f"Exit price out of range: {exit_dec}")
            
            if not (1 <= quantity <= MAX_QUANTITY):
                raise ValueError(f"Quantity out of range: {quantity}")
            
            if side.upper() not in ['YES', 'NO', 'BUY', 'SELL']:
                raise ValueError(f"Invalid side: {side}")
            
            # Calculate P&L based on position direction
            if side.upper() in ['YES', 'BUY']:
                pnl_per_share = exit_dec - entry_dec
            else:  # NO or SELL
                pnl_per_share = entry_dec - exit_dec
            
            total_pnl = pnl_per_share * Decimal(str(quantity))
            
            return total_pnl.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
        except Exception as e:
            logger.error(f"Error calculating P&L: {e}")
            return Decimal('0')

    @staticmethod
    def validate_trade_limits(
        position_value: Union[str, float, int, Decimal],
        account_balance: Union[str, float, int, Decimal],
        max_position_pct: Union[str, float, int, Decimal] = Decimal('0.05'),
        max_daily_loss_pct: Union[str, float, int, Decimal] = Decimal('0.10'),
        daily_pnl: Union[str, float, int, Decimal] = Decimal('0')
    ) -> bool:
        """
        Validate trade against risk limits.
        
        Returns:
            True if trade is within limits, False otherwise
        """
        try:
            pos_value_dec = SecureFinancialCalculator.safe_decimal_conversion(position_value, "position_value")
            balance_dec = SecureFinancialCalculator.safe_decimal_conversion(account_balance, "account_balance")
            max_pos_pct_dec = SecureFinancialCalculator.safe_decimal_conversion(max_position_pct, "max_position_pct")
            max_loss_pct_dec = SecureFinancialCalculator.safe_decimal_conversion(max_daily_loss_pct, "max_daily_loss_pct")
            daily_pnl_dec = SecureFinancialCalculator.safe_decimal_conversion(daily_pnl, "daily_pnl")
            
            # Check position size limit
            max_position_value = balance_dec * max_pos_pct_dec
            if pos_value_dec > max_position_value:
                logger.warning(
                    f"Position exceeds size limit: ${pos_value_dec} > ${max_position_value}"
                )
                return False
            
            # Check daily loss limit (if currently losing)
            if daily_pnl_dec < 0:
                max_daily_loss = balance_dec * max_loss_pct_dec
                if abs(daily_pnl_dec) > max_daily_loss:
                    logger.warning(
                        f"Daily loss limit exceeded: ${abs(daily_pnl_dec)} > ${max_daily_loss}"
                    )
                    return False
            
            # Check minimum balance requirement
            if balance_dec < MIN_BALANCE:
                logger.warning(f"Balance below minimum: ${balance_dec}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating trade limits: {e}")
            return False


# Convenience functions
def safe_calculate_quantity(balance: float, price: float, position_pct: float = 0.03) -> int:
    """Safe wrapper for quantity calculation."""
    return SecureFinancialCalculator.calculate_position_quantity(
        balance=balance,
        market_price=price,
        position_size_pct=position_pct
    )


def safe_price_to_cents(price: float) -> int:
    """Safe wrapper for price conversion."""
    return SecureFinancialCalculator.calculate_price_in_cents(price)


def safe_position_value(price: float, quantity: int) -> float:
    """Safe wrapper for position value calculation."""
    result = SecureFinancialCalculator.calculate_position_value(price, quantity)
    return float(result)