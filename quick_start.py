#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Quick Start & Validation Script
This script validates your setup and helps you get started quickly.
"""

import os
import sys
import json
import importlib
from datetime import datetime
from colorama import init, Fore, Style

# Initialize colorama for cross-platform colored output
init(autoreset=True)

def print_header():
    """Print welcome header"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.CYAN}    Weather Trading Bot v2.0 - Quick Start")
    print(f"{Fore.CYAN}{'='*60}\n")

def check_environment():
    """Check environment variables"""
    print(f"{Fore.YELLOW}🔍 Checking Environment Variables...")
    
    required_vars = {
        'KALSHI_API_KEY': 'Kalshi API Key',
        'KALSHI_API_SECRET': 'Kalshi API Secret',
        'XAI_API_KEY': 'XAI API Key (for LLM)'
    }
    
    missing = []
    for var, description in required_vars.items():
        if os.getenv(var):
            print(f"  {Fore.GREEN}✓ {description}: Set")
        else:
            print(f"  {Fore.RED}✗ {description}: Missing")
            missing.append(var)
    
    if missing:
        print(f"\n{Fore.RED}⚠️  Missing environment variables: {', '.join(missing)}")
        print(f"{Fore.YELLOW}   Set them in your .env file or export them:")
        for var in missing:
            print(f"   export {var}='your_value_here'")
        return False
    
    # Check optional but recommended
    env_mode = os.getenv('KALSHI_ENV', 'demo')
    print(f"\n  {Fore.BLUE}ℹ️  Kalshi Environment: {env_mode}")
    if env_mode == 'demo':
        print(f"  {Fore.YELLOW}   Note: Running in DEMO mode. Set KALSHI_ENV=production for live trading")
    
    return True

def check_dependencies():
    """Check Python dependencies"""
    print(f"\n{Fore.YELLOW}📦 Checking Dependencies...")
    
    required_packages = [
        ('kalshi_python', 'kalshi-python'),
        ('metar', 'python-metar'),
        ('siphon', 'siphon'),
        ('metpy', 'metpy'),
        ('scipy', 'scipy'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('requests', 'requests'),
        ('dateutil', 'python-dateutil')
    ]
    
    missing = []
    for module_name, package_name in required_packages:
        try:
            importlib.import_module(module_name)
            print(f"  {Fore.GREEN}✓ {package_name}")
        except ImportError:
            print(f"  {Fore.RED}✗ {package_name}")
            missing.append(package_name)
    
    if missing:
        print(f"\n{Fore.RED}⚠️  Missing packages: {', '.join(missing)}")
        print(f"{Fore.YELLOW}   Install with: pip install {' '.join(missing)}")
        return False
    
    return True

def check_file_structure():
    """Check required files exist"""
    print(f"\n{Fore.YELLOW}📁 Checking File Structure...")
    
    required_files = [
        'kalshi-ai-trading-bot/src/weather/__init__.py',
        'kalshi-ai-trading-bot/src/weather/noaa_client.py',
        'kalshi-ai-trading-bot/src/weather/weather_analyzer.py',
        'kalshi-ai-trading-bot/src/weather/weather_analyzer_enhanced.py',
        'kalshi-ai-trading-bot/src/strategies/weather_trading.py',
        'kalshi-ai-trading-bot/weather_dashboard_extension.py',
        'deploy_weather_bot.py'
    ]
    
    missing = []
    for filepath in required_files:
        if os.path.exists(filepath):
            print(f"  {Fore.GREEN}✓ {filepath}")
        else:
            print(f"  {Fore.RED}✗ {filepath}")
            missing.append(filepath)
    
    if missing:
        print(f"\n{Fore.RED}⚠️  Missing files detected!")
        print(f"{Fore.YELLOW}   The implementation may be incomplete.")
        return False
    
    return True

def test_noaa_connection():
    """Test NOAA data connection"""
    print(f"\n{Fore.YELLOW}🌡️  Testing NOAA Data Connection...")
    
    try:
        # Add kalshi-ai-trading-bot to path
        sys.path.insert(0, 'kalshi-ai-trading-bot')
        from src.weather.noaa_client import NOAAClient
        
        client = NOAAClient()
        
        # Test METAR for NYC
        print(f"  {Fore.BLUE}Testing METAR data for NYC (KNYC)...")
        metar_data = client.get_metar_observation('KNYC')
        
        if metar_data and 'temperature' in metar_data:
            temp_f = metar_data['temperature'] * 9/5 + 32 if metar_data['temperature'] else 'N/A'
            print(f"  {Fore.GREEN}✓ Current NYC Temperature: {temp_f:.1f}°F")
        else:
            print(f"  {Fore.YELLOW}⚠️  METAR data retrieved but temperature not available")
        
        return True
        
    except Exception as e:
        print(f"  {Fore.RED}✗ Failed to connect to NOAA: {str(e)}")
        return False

def test_kalshi_connection():
    """Test Kalshi API connection"""
    print(f"\n{Fore.YELLOW}💰 Testing Kalshi Connection...")
    
    try:
        # Check if we're in production or demo mode
        env_mode = os.getenv('KALSHI_ENV', 'demo')
        
        if env_mode == 'demo':
            print(f"  {Fore.BLUE}ℹ️  Skipping Kalshi test in DEMO mode")
            print(f"  {Fore.YELLOW}   Set KALSHI_ENV=production to test live connection")
            return True
        
        # Add kalshi-ai-trading-bot to path if not already
        if 'kalshi-ai-trading-bot' not in sys.path:
            sys.path.insert(0, 'kalshi-ai-trading-bot')
        
        from src.agents.kalshi_client import KalshiClient
        
        client = KalshiClient()
        
        # Test by getting exchange status
        print(f"  {Fore.BLUE}Testing API connection...")
        # This would normally test the connection
        # For safety, we'll just verify the client initializes
        print(f"  {Fore.GREEN}✓ Kalshi client initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"  {Fore.RED}✗ Failed to connect to Kalshi: {str(e)}")
        return False

def run_mode_selection():
    """Help user select running mode"""
    print(f"\n{Fore.CYAN}🚀 Ready to Start!")
    print(f"\n{Fore.YELLOW}Select running mode:")
    print(f"  1. {Fore.BLUE}Paper Trading{Style.RESET_ALL} (Recommended for first 3 days)")
    print(f"  2. {Fore.GREEN}Limited Production{Style.RESET_ALL} (50% risk limits)")
    print(f"  3. {Fore.RED}Full Production{Style.RESET_ALL} (Full risk limits)")
    print(f"  4. {Fore.MAGENTA}Test Mode{Style.RESET_ALL} (Single cycle test)")
    
    while True:
        try:
            choice = input(f"\n{Fore.CYAN}Enter choice (1-4): {Style.RESET_ALL}")
            if choice in ['1', '2', '3', '4']:
                break
            print(f"{Fore.RED}Invalid choice. Please enter 1, 2, 3, or 4.")
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}Cancelled by user")
            return None
    
    commands = {
        '1': 'python deploy_weather_bot.py --mode=paper',
        '2': 'python deploy_weather_bot.py --mode=production --max-risk=0.5',
        '3': 'python deploy_weather_bot.py --mode=production',
        '4': 'python test_weather_data.py'
    }
    
    return commands[choice]

def main():
    """Main quick start flow"""
    print_header()
    
    print(f"{Fore.CYAN}Starting validation checks...\n")
    
    # Track overall status
    all_good = True
    
    # Run checks
    if not check_environment():
        all_good = False
    
    if not check_dependencies():
        all_good = False
    
    if not check_file_structure():
        all_good = False
    
    # Only test connections if basics are OK
    if all_good:
        if not test_noaa_connection():
            print(f"{Fore.YELLOW}⚠️  NOAA connection issue - bot can still run with reduced functionality")
        
        if not test_kalshi_connection():
            print(f"{Fore.YELLOW}⚠️  Kalshi connection not tested - ensure credentials are correct")
    
    # Summary
    print(f"\n{Fore.CYAN}{'='*60}")
    if all_good:
        print(f"{Fore.GREEN}✅ System Validation: PASSED")
        print(f"{Fore.GREEN}   Your Weather Trading Bot is ready to run!")
        
        # Get run command
        command = run_mode_selection()
        if command:
            print(f"\n{Fore.CYAN}To start the bot, run:")
            print(f"{Fore.YELLOW}  {command}")
            print(f"\n{Fore.BLUE}Monitor the dashboard at: http://localhost:8080")
            print(f"{Fore.BLUE}Check logs at: kalshi-ai-trading-bot/logs/weather_bot.log")
            
            # Ask if they want to run now
            run_now = input(f"\n{Fore.CYAN}Run this command now? (y/n): {Style.RESET_ALL}").lower()
            if run_now == 'y':
                print(f"\n{Fore.GREEN}Starting Weather Trading Bot...")
                os.system(command)
        
    else:
        print(f"{Fore.RED}❌ System Validation: FAILED")
        print(f"{Fore.YELLOW}   Please fix the issues above before running the bot.")
        print(f"\n{Fore.BLUE}For help, see:")
        print(f"  - README_WEATHER_BOT.md")
        print(f"  - IMPLEMENTATION_COMPLETE.md")
    
    print(f"{Fore.CYAN}{'='*60}\n")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Quick start cancelled by user")
    except Exception as e:
        print(f"\n{Fore.RED}Error: {str(e)}")
        print(f"{Fore.YELLOW}Please check your setup and try again")
