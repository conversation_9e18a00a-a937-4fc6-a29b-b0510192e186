#!/usr/bin/env python3
"""
Weather Trading Bot v2.0 - Historical Data Fetcher

This script fetches historical weather data for backtesting.
All data sources are FREE and publicly available!
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
import json
import sqlite3
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HistoricalWeatherFetcher:
    """
    Fetches historical weather data from multiple FREE sources.
    All of these APIs are publicly available!
    """
    
    def __init__(self, db_path: str = "historical_weather.db"):
        self.db_path = db_path
        self.stations = {
            'KNYC': {'name': 'New York Central Park', 'wfo': 'OKX'},
            'KMIA': {'name': 'Miami International', 'wfo': 'MFL'},
            'KMDW': {'name': 'Chicago Midway', 'wfo': 'LOT'},
            'KDEN': {'name': 'Denver International', 'wfo': 'BOU'},
            'KAUS': {'name': 'Austin Bergstrom', 'wfo': 'EWX'},
            'KLAX': {'name': 'Los Angeles International', 'wfo': 'LOX'},
            'KPHL': {'name': 'Philadelphia International', 'wfo': 'PHI'}
        }
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for historical data storage."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS historical_observations (
                station_id TEXT,
                observation_time TIMESTAMP,
                temperature REAL,
                temperature_max_24h REAL,
                dewpoint REAL,
                wind_speed REAL,
                wind_direction INTEGER,
                pressure REAL,
                PRIMARY KEY (station_id, observation_time)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS historical_settlements (
                station_id TEXT,
                date DATE,
                official_high REAL,
                official_low REAL,
                precipitation REAL,
                PRIMARY KEY (station_id, date)
            )
        """)
        
        conn.commit()
        conn.close()
        logger.info(f"Database initialized at {self.db_path}")
    
    async def fetch_iowa_mesonet_data(self, station: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """
        Fetch historical data from Iowa Environmental Mesonet.
        This is a FREE service with extensive historical data!
        """
        url = "https://mesonet.agron.iastate.edu/cgi-bin/request/asos.py"
        
        params = {
            'station': station,
            'data': 'all',
            'year1': start_date.year,
            'month1': start_date.month,
            'day1': start_date.day,
            'year2': end_date.year,
            'month2': end_date.month,
            'day2': end_date.day,
            'tz': 'UTC',
            'format': 'comma',
            'latlon': 'no',
            'elev': 'no',
            'missing': 'null',
            'trace': 'null',
            'direct': 'no',
            'report_type': '3'  # METAR + specials
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        text = await response.text()
                        
                        # Skip header lines and parse CSV
                        lines = text.split('\n')
                        data_lines = [line for line in lines if not line.startswith('#') and line.strip()]
                        
                        if data_lines:
                            # Create DataFrame
                            import io
                            df = pd.read_csv(io.StringIO('\n'.join(data_lines)))
                            
                            # Convert valid column to datetime
                            if 'valid' in df.columns:
                                df['valid'] = pd.to_datetime(df['valid'])
                            
                            logger.info(f"Fetched {len(df)} records from IEM for {station}")
                            return df
                        else:
                            logger.warning(f"No data returned from IEM for {station}")
                            return pd.DataFrame()
                    else:
                        logger.error(f"IEM API error: {response.status}")
                        return pd.DataFrame()
                        
        except Exception as e:
            logger.error(f"Error fetching IEM data: {e}")
            return pd.DataFrame()
    
    async def fetch_noaa_climate_data(self, station: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """
        Fetch daily climate summaries from NOAA.
        These contain the OFFICIAL high/low temperatures used for settlement!
        """
        # NOAA Climate Data Online API
        # Note: Requires free API token from https://www.ncdc.noaa.gov/cdo-web/token
        
        # For now, use the Global Summary of the Day (GSOD) dataset
        url = f"https://www.ncei.noaa.gov/data/global-summary-of-the-day/access/{start_date.year}/{station}.csv"
        
        try:
            df = pd.read_csv(url, parse_dates=['DATE'])
            
            # Convert temperatures from Celsius to Fahrenheit
            if 'MAX' in df.columns:
                df['max_temp_f'] = df['MAX'] * 9/5 + 32
            if 'MIN' in df.columns:
                df['min_temp_f'] = df['MIN'] * 9/5 + 32
            
            logger.info(f"Fetched {len(df)} daily summaries for {station}")
            return df
            
        except Exception as e:
            logger.warning(f"Could not fetch NOAA climate data: {e}")
            return pd.DataFrame()
    
    async def fetch_metar_archive(self, station: str, date: datetime) -> List[Dict]:
        """
        Fetch historical METAR observations for a specific date.
        """
        observations = []
        
        # NOAA ADDS historical data
        url = "https://aviationweather.gov/adds/dataserver_current/httpparam"
        
        params = {
            'dataSource': 'metars',
            'requestType': 'retrieve',
            'format': 'xml',
            'stationString': station,
            'startTime': date.isoformat(),
            'endTime': (date + timedelta(days=1)).isoformat(),
            'hoursBeforeNow': 0
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        # Parse XML response (simplified)
                        text = await response.text()
                        # Would need XML parsing here
                        logger.info(f"Fetched METAR data for {station} on {date}")
                        
        except Exception as e:
            logger.error(f"Error fetching METAR archive: {e}")
        
        return observations
    
    def store_observations(self, station: str, df: pd.DataFrame):
        """Store observations in the database."""
        if df.empty:
            return
        
        conn = sqlite3.connect(self.db_path)
        
        # Prepare data for insertion
        if 'valid' in df.columns and 'tmpf' in df.columns:
            for _, row in df.iterrows():
                try:
                    conn.execute("""
                        INSERT OR REPLACE INTO historical_observations 
                        (station_id, observation_time, temperature, dewpoint, wind_speed, wind_direction)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        station,
                        row.get('valid'),
                        row.get('tmpf'),
                        row.get('dwpf'),
                        row.get('sknt'),
                        row.get('drct')
                    ))
                except Exception as e:
                    logger.debug(f"Error inserting row: {e}")
        
        conn.commit()
        conn.close()
        logger.info(f"Stored {len(df)} observations for {station}")
    
    def store_settlements(self, station: str, df: pd.DataFrame):
        """Store daily settlement values in the database."""
        if df.empty:
            return
        
        conn = sqlite3.connect(self.db_path)
        
        if 'DATE' in df.columns and 'max_temp_f' in df.columns:
            for _, row in df.iterrows():
                try:
                    conn.execute("""
                        INSERT OR REPLACE INTO historical_settlements 
                        (station_id, date, official_high, official_low, precipitation)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        station,
                        row.get('DATE'),
                        row.get('max_temp_f'),
                        row.get('min_temp_f'),
                        row.get('PRCP', 0)
                    ))
                except Exception as e:
                    logger.debug(f"Error inserting settlement: {e}")
        
        conn.commit()
        conn.close()
        logger.info(f"Stored {len(df)} settlement values for {station}")
    
    async def fetch_all_historical_data(self, days_back: int = 365):
        """
        Fetch historical data for all stations.
        Default: 1 year of data
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        logger.info(f"Fetching historical data from {start_date} to {end_date}")
        
        for station_id, station_info in self.stations.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"Processing {station_id} - {station_info['name']}")
            logger.info(f"{'='*50}")
            
            # Fetch from Iowa Environmental Mesonet
            iem_data = await self.fetch_iowa_mesonet_data(station_id, start_date, end_date)
            if not iem_data.empty:
                self.store_observations(station_id, iem_data)
            
            # Fetch daily summaries
            climate_data = await self.fetch_noaa_climate_data(station_id, start_date, end_date)
            if not climate_data.empty:
                self.store_settlements(station_id, climate_data)
            
            # Small delay to be nice to the APIs
            await asyncio.sleep(1)
        
        logger.info("\n✅ Historical data fetch complete!")
        self.print_summary()
    
    def print_summary(self):
        """Print a summary of the collected data."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        print("\n" + "="*60)
        print("HISTORICAL DATA SUMMARY")
        print("="*60)
        
        for station in self.stations.keys():
            # Count observations
            cursor.execute(
                "SELECT COUNT(*) FROM historical_observations WHERE station_id = ?",
                (station,)
            )
            obs_count = cursor.fetchone()[0]
            
            # Count settlements
            cursor.execute(
                "SELECT COUNT(*) FROM historical_settlements WHERE station_id = ?",
                (station,)
            )
            settlement_count = cursor.fetchone()[0]
            
            # Get date range
            cursor.execute(
                "SELECT MIN(observation_time), MAX(observation_time) FROM historical_observations WHERE station_id = ?",
                (station,)
            )
            date_range = cursor.fetchone()
            
            print(f"\n{station}:")
            print(f"  Observations: {obs_count:,}")
            print(f"  Daily settlements: {settlement_count:,}")
            if date_range[0]:
                print(f"  Date range: {date_range[0][:10]} to {date_range[1][:10]}")
        
        conn.close()
        print("\n" + "="*60)
    
    def export_to_csv(self, output_dir: str = "historical_data"):
        """Export collected data to CSV files for analysis."""
        Path(output_dir).mkdir(exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        
        # Export observations
        obs_df = pd.read_sql_query(
            "SELECT * FROM historical_observations ORDER BY station_id, observation_time",
            conn
        )
        obs_df.to_csv(f"{output_dir}/historical_observations.csv", index=False)
        logger.info(f"Exported {len(obs_df)} observations to CSV")
        
        # Export settlements
        settlement_df = pd.read_sql_query(
            "SELECT * FROM historical_settlements ORDER BY station_id, date",
            conn
        )
        settlement_df.to_csv(f"{output_dir}/historical_settlements.csv", index=False)
        logger.info(f"Exported {len(settlement_df)} settlements to CSV")
        
        conn.close()


async def main():
    """Main function to fetch historical data."""
    
    print("="*60)
    print("Weather Trading Bot - Historical Data Fetcher")
    print("="*60)
    print("\nThis will fetch FREE historical weather data for backtesting.")
    print("Data sources:")
    print("  - Iowa Environmental Mesonet (observations)")
    print("  - NOAA Climate Data (daily summaries)")
    print("\n")
    
    # Ask user for date range
    try:
        days = int(input("How many days of historical data to fetch? (default: 365): ") or "365")
    except ValueError:
        days = 365
    
    print(f"\nFetching {days} days of historical data...")
    print("This may take a few minutes...\n")
    
    # Create fetcher and run
    fetcher = HistoricalWeatherFetcher()
    await fetcher.fetch_all_historical_data(days_back=days)
    
    # Export to CSV
    export = input("\nExport data to CSV files? (y/n): ")
    if export.lower() == 'y':
        fetcher.export_to_csv()
        print("✅ Data exported to 'historical_data' directory")
    
    print("\n✅ Historical data is ready for backtesting!")
    print(f"Database location: {fetcher.db_path}")


if __name__ == "__main__":
    asyncio.run(main())
