"""
Weather Trading Strategy - Extends UnifiedAdvancedTradingSystem

This strategy specializes in weather derivatives trading by:
- Identifying weather markets (NHIGH, CHIHIGH, etc.)
- Using real-time NOAA data for decision making
- Leveraging meteorologist insights from AFDs
- Managing weather-specific risks
"""

import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from src.clients.kalshi_client import KalshiClient
from src.clients.xai_client import XAIClient
from src.utils.database import DatabaseManager, Market, Position
from src.utils.logging_setup import get_trading_logger
from src.strategies.unified_trading_system import (
    UnifiedAdvancedTradingSystem,
    TradingSystemConfig,
    TradingSystemResults
)
from src.strategies.portfolio_optimization import MarketOpportunity
from src.weather.noaa_client import NOAAClient
from src.weather.weather_analyzer import WeatherAnalyzer, WeatherAnalysis


@dataclass
class WeatherTradingConfig(TradingSystemConfig):
    """Configuration specific to weather trading."""
    
    # Weather-specific parameters
    min_edge_threshold: float = 0.08  # Minimum edge to enter position
    max_weather_exposure: float = 0.50  # Max 50% of capital in weather
    afd_confidence_weight: float = 0.30  # Weight given to meteorologist confidence
    model_consensus_weight: float = 0.40  # Weight given to model agreement
    observation_weight: float = 0.30  # Weight given to current observations
    
    # Risk parameters
    max_single_weather_position: float = 0.10  # Max 10% in single weather market
    min_hours_to_expiry: int = 3  # Don't trade if < 3 hours to expiry
    sensor_anomaly_threshold: float = 5.0  # Degrees for anomaly detection
    
    # Settlement parameters
    cli_verification_enabled: bool = True
    backup_station_validation: bool = True


class WeatherTradingStrategy(UnifiedAdvancedTradingSystem):
    """
    Weather derivatives trading strategy.
    
    Extends the UnifiedAdvancedTradingSystem with weather-specific logic
    while reusing all the existing infrastructure for risk management,
    portfolio optimization, and trade execution.
    """
    
    def __init__(
        self,
        db_manager: DatabaseManager,
        kalshi_client: KalshiClient,
        xai_client: XAIClient,
        config: Optional[WeatherTradingConfig] = None
    ):
        # Initialize parent class
        super().__init__(db_manager, kalshi_client, xai_client, config or WeatherTradingConfig())
        
        # Weather-specific components
        self.noaa_client = NOAAClient()
        self.weather_analyzer = WeatherAnalyzer(xai_client, self.noaa_client)
        self.weather_config = config or WeatherTradingConfig()
        self.logger = get_trading_logger("weather_trading")
        
        # Weather market identifiers
        self.WEATHER_MARKETS = {
            'NHIGH': 'KNYC',    # New York
            'MIAHIGH': 'KMIA',  # Miami
            'CHIHIGH': 'KMDW',  # Chicago
            'DENHIGH': 'KDEN',  # Denver
            'AUSHIGH': 'KAUS',  # Austin
            'LAHIGH': 'KLAX',   # Los Angeles
            'PHILHIGH': 'KPHL'  # Philadelphia
        }
        
        self.PRECIPITATION_MARKETS = {
            'RAINNYC': ['KNYC', 'KJFK', 'KLGA', 'KEWR']  # NYC rainfall
        }
    
    async def execute_weather_trading_strategy(self) -> TradingSystemResults:
        """
        Execute weather-specific trading strategy.
        
        This method orchestrates weather data collection, analysis,
        and trading decisions while leveraging the parent class's
        risk management and execution infrastructure.
        """
        self.logger.info("🌡️ Executing Weather Trading Strategy")
        
        try:
            # Step 1: Identify weather markets on Kalshi
            weather_markets = await self._identify_weather_markets()
            if not weather_markets:
                self.logger.warning("No weather markets available")
                return TradingSystemResults()
            
            self.logger.info(f"Found {len(weather_markets)} weather markets")
            
            # Step 2: Get current weather observations
            observations = await self.noaa_client.get_all_station_observations()
            self.logger.info(f"Retrieved observations from {len(observations)} stations")
            
            # Step 3: Analyze each weather market
            opportunities = []
            for market in weather_markets:
                opportunity = await self._analyze_weather_market(market, observations)
                if opportunity:
                    opportunities.append(opportunity)
            
            self.logger.info(f"Identified {len(opportunities)} weather opportunities")
            
            # Step 4: Filter by edge and confidence
            viable_opportunities = [
                opp for opp in opportunities
                if opp.edge > self.weather_config.min_edge_threshold
                and opp.confidence > 0.60
            ]
            
            self.logger.info(f"Filtered to {len(viable_opportunities)} viable opportunities")
            
            # Step 5: Use parent's portfolio optimization
            if viable_opportunities:
                # Allocate weather-specific capital
                original_capital = self.portfolio_optimizer.total_capital
                self.portfolio_optimizer.total_capital = self.total_capital * self.weather_config.max_weather_exposure
                
                # Optimize portfolio
                allocation = await self.portfolio_optimizer.optimize_portfolio(viable_opportunities)
                
                # Execute allocations
                if allocation and allocation.allocations:
                    execution_results = await self._execute_portfolio_allocations(
                        allocation, viable_opportunities
                    )
                    self.logger.info(f"Executed {execution_results['positions_created']} weather positions")
                
                # Restore capital
                self.portfolio_optimizer.total_capital = original_capital
                
                # Compile results
                results = TradingSystemResults(
                    directional_positions=len(allocation.allocations),
                    directional_exposure=allocation.total_capital_used,
                    directional_expected_return=allocation.expected_portfolio_return,
                    portfolio_sharpe_ratio=allocation.portfolio_sharpe,
                    portfolio_volatility=allocation.portfolio_volatility,
                    total_capital_used=allocation.total_capital_used,
                    portfolio_expected_return=allocation.expected_portfolio_return,
                    total_positions=len(allocation.allocations),
                    capital_efficiency=allocation.total_capital_used / self.total_capital
                )
            else:
                self.logger.info("No viable weather opportunities after filtering")
                results = TradingSystemResults()
            
            # Step 6: Monitor existing weather positions
            await self._monitor_weather_positions()
            
            self.logger.info(
                f"🌡️ Weather Strategy Complete: "
                f"{results.total_positions} positions, "
                f"${results.total_capital_used:.0f} deployed, "
                f"Expected return: {results.portfolio_expected_return:.2%}"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in weather trading strategy: {e}")
            return TradingSystemResults()
    
    async def _identify_weather_markets(self) -> List[Market]:
        """
        Identify weather markets available on Kalshi.
        
        Filters all markets to find weather-specific ones.
        """
        try:
            # Get all markets from database
            all_markets = await self.db_manager.get_eligible_markets(
                volume_min=100,  # Lower volume threshold for weather
                max_days_to_expiry=7  # Weather markets typically daily
            )
            
            if not all_markets:
                return []
            
            # Filter for weather markets
            weather_markets = []
            for market in all_markets:
                # Check if market ticker matches weather patterns
                ticker = getattr(market, 'ticker', '').upper()
                
                # Temperature markets
                if any(weather_ticker in ticker for weather_ticker in self.WEATHER_MARKETS.keys()):
                    weather_markets.append(market)
                    self.logger.info(f"Found temperature market: {ticker}")
                
                # Precipitation markets
                elif any(precip_ticker in ticker for precip_ticker in self.PRECIPITATION_MARKETS.keys()):
                    weather_markets.append(market)
                    self.logger.info(f"Found precipitation market: {ticker}")
            
            return weather_markets
            
        except Exception as e:
            self.logger.error(f"Error identifying weather markets: {e}")
            return []
    
    async def _analyze_weather_market(
        self, 
        market: Market,
        observations: Dict
    ) -> Optional[MarketOpportunity]:
        """
        Analyze a specific weather market for trading opportunity.
        
        Converts weather analysis into MarketOpportunity for portfolio optimization.
        """
        try:
            ticker = getattr(market, 'ticker', '').upper()
            
            # Get station for this market
            station_id = None
            for weather_ticker, station in self.WEATHER_MARKETS.items():
                if weather_ticker in ticker:
                    station_id = station
                    break
            
            if not station_id:
                self.logger.warning(f"No station mapping for {ticker}")
                return None
            
            # Get current observation
            if station_id not in observations:
                self.logger.warning(f"No observation for {station_id}")
                return None
            
            obs = observations[station_id]
            
            # Get market data
            market_data = await self.kalshi_client.get_market(market.market_id)
            if not market_data:
                return None
            
            market_info = market_data.get('market', {})
            yes_price = market_info.get('yes_price', 50) / 100.0
            
            # Extract strike price from market (simplified - would parse from market details)
            strike_price = self._extract_strike_price(market_info)
            
            # Analyze weather opportunity
            analysis = await self.weather_analyzer.analyze_weather_opportunity(
                station_id=station_id,
                market_price=yes_price,
                strike_price=strike_price
            )
            
            # Skip if no edge or low confidence
            if analysis.edge < self.weather_config.min_edge_threshold:
                self.logger.info(f"{ticker}: Edge too small ({analysis.edge:.3f})")
                return None
            
            if analysis.confidence_score < 60:
                self.logger.info(f"{ticker}: Confidence too low ({analysis.confidence_score:.0f}%)")
                return None
            
            # Create MarketOpportunity
            opportunity = MarketOpportunity(
                market_id=market.market_id,
                ticker=ticker,
                event_description=f"Temperature > {strike_price}°F at {station_id}",
                yes_price=yes_price,
                no_price=1 - yes_price,
                volume=getattr(market, 'volume', 0),
                expiry_date=getattr(market, 'close_time', datetime.now() + timedelta(days=1)),
                ai_confidence=analysis.confidence_score / 100.0,
                edge=analysis.edge,
                confidence=analysis.confidence_score / 100.0,
                expected_return=analysis.edge * 100,  # Simplified return calculation
                volatility=0.2,  # Default volatility for weather
                sharpe_ratio=analysis.edge / 0.2,  # Simplified Sharpe
                correlation_with_market=0.1,  # Low correlation with general market
                time_to_expiry=(getattr(market, 'close_time', datetime.now() + timedelta(days=1)) - datetime.now()).total_seconds() / 3600
            )
            
            self.logger.info(
                f"📊 {ticker}: Current={analysis.current_temp:.1f}°F, "
                f"Strike={strike_price}°F, Edge={analysis.edge:.3f}, "
                f"Confidence={analysis.confidence_score:.0f}%"
            )
            
            return opportunity
            
        except Exception as e:
            self.logger.error(f"Error analyzing weather market {market.market_id}: {e}")
            return None
    
    def _extract_strike_price(self, market_info: Dict) -> float:
        """
        Extract strike price from market information.
        
        In production, would parse from market title or metadata.
        """
        # Default strike prices by market type
        defaults = {
            'NHIGH': 85,
            'MIAHIGH': 90,
            'CHIHIGH': 85,
            'DENHIGH': 90,
            'AUSHIGH': 100,
            'LAHIGH': 80,
            'PHILHIGH': 85
        }
        
        # Try to parse from title
        title = market_info.get('title', '')
        
        # Look for temperature in title (e.g., "Will NYC exceed 85°F?")
        import re
        temp_match = re.search(r'(\d+)°?F', title)
        if temp_match:
            return float(temp_match.group(1))
        
        # Fall back to defaults
        for market_type, default_strike in defaults.items():
            if market_type in title.upper():
                return default_strike
        
        return 85.0  # Global default
    
    async def _monitor_weather_positions(self):
        """
        Monitor existing weather positions for exit signals.
        
        Checks for:
        - Temperature reaching/exceeding strike
        - Time decay approaching expiry
        - Forecast changes
        - Sensor anomalies
        """
        try:
            # Get open weather positions
            positions = await self.db_manager.get_open_positions()
            weather_positions = [
                pos for pos in positions
                if any(weather_ticker in getattr(pos, 'market_id', '').upper() 
                      for weather_ticker in self.WEATHER_MARKETS.keys())
            ]
            
            if not weather_positions:
                return
            
            self.logger.info(f"Monitoring {len(weather_positions)} weather positions")
            
            for position in weather_positions:
                await self._check_weather_exit_signals(position)
                
        except Exception as e:
            self.logger.error(f"Error monitoring weather positions: {e}")
    
    async def _check_weather_exit_signals(self, position: Position):
        """
        Check if a weather position should be exited.
        
        Weather-specific exit signals:
        - Temperature exceeded strike (take profit)
        - Peak temperature passed (exit if not hit)
        - Forecast materially changed (re-evaluate)
        - < 2 hours to expiry (time decay)
        """
        try:
            market_id = getattr(position, 'market_id', '')
            
            # Get station for this position
            station_id = None
            for weather_ticker, station in self.WEATHER_MARKETS.items():
                if weather_ticker in market_id.upper():
                    station_id = station
                    break
            
            if not station_id:
                return
            
            # Get current observation
            obs = await self.noaa_client.get_current_observation(station_id)
            if not obs:
                return
            
            # Get market data for strike price
            market_data = await self.kalshi_client.get_market(market_id)
            if not market_data:
                return
            
            strike_price = self._extract_strike_price(market_data.get('market', {}))
            
            # Check exit conditions
            exit_signal = False
            exit_reason = ""
            
            # 1. Temperature exceeded strike (winner!)
            if obs.temperature_f >= strike_price:
                exit_signal = True
                exit_reason = f"Temperature {obs.temperature_f:.1f}°F exceeded strike {strike_price}°F"
            
            # 2. Past peak time and not hit
            elif datetime.now().hour >= 16:  # Past 4 PM local
                if obs.temperature_f < strike_price - 5:
                    exit_signal = True
                    exit_reason = f"Past peak time, unlikely to hit {strike_price}°F"
            
            # 3. Time decay
            market_info = market_data.get('market', {})
            close_time = datetime.fromisoformat(market_info.get('close_time', ''))
            hours_to_expiry = (close_time - datetime.now()).total_seconds() / 3600
            
            if hours_to_expiry < 2:
                exit_signal = True
                exit_reason = f"Only {hours_to_expiry:.1f} hours to expiry"
            
            # 4. Sensor anomaly
            if abs(obs.temperature_f - getattr(position, 'entry_price', 70) * 100) > 30:
                self.logger.warning(f"Possible sensor anomaly: {obs.temperature_f:.1f}°F")
            
            if exit_signal:
                self.logger.info(f"🚪 Exit signal for {market_id}: {exit_reason}")
                # Would trigger exit through parent's execution system
                
        except Exception as e:
            self.logger.error(f"Error checking exit signals: {e}")
    
    async def verify_settlement(self, market_id: str) -> Dict:
        """
        Verify weather market settlement using multiple sources.
        
        Uses:
        - CLI reports (official)
        - METAR 24-hour max
        - Backup station validation
        """
        try:
            # Get station for this market
            station_id = None
            for weather_ticker, station in self.WEATHER_MARKETS.items():
                if weather_ticker in market_id.upper():
                    station_id = station
                    break
            
            if not station_id:
                return {'verified': False, 'reason': 'Unknown market'}
            
            # Get CLI report (official settlement source)
            cli_report = await self.noaa_client.get_cli_report(station_id)
            
            # Get current METAR with 24-hour max
            obs = await self.noaa_client.get_current_observation(station_id)
            
            verification = {
                'verified': False,
                'cli_max': cli_report.get('max_temperature') if cli_report else None,
                'metar_24hr_max': obs.twenty_four_hour_max if obs else None,
                'discrepancy': None
            }
            
            # Check for discrepancies
            if cli_report and obs:
                if cli_report.get('max_temperature') and obs.twenty_four_hour_max:
                    discrepancy = abs(cli_report['max_temperature'] - obs.twenty_four_hour_max)
                    verification['discrepancy'] = discrepancy
                    
                    if discrepancy > 1.0:
                        self.logger.warning(
                            f"Settlement discrepancy for {station_id}: "
                            f"CLI={cli_report['max_temperature']:.1f}°F, "
                            f"METAR={obs.twenty_four_hour_max:.1f}°F"
                        )
                    
                    verification['verified'] = discrepancy <= 1.0
            
            return verification
            
        except Exception as e:
            self.logger.error(f"Error verifying settlement: {e}")
            return {'verified': False, 'reason': str(e)}
    
    def get_weather_summary(self) -> Dict:
        """
        Get summary of weather trading performance.
        """
        return {
            'strategy': 'weather_trading',
            'markets_tracked': list(self.WEATHER_MARKETS.keys()),
            'stations_monitored': len(self.WEATHER_MARKETS),
            'config': {
                'min_edge': self.weather_config.min_edge_threshold,
                'max_exposure': self.weather_config.max_weather_exposure,
                'min_hours_to_expiry': self.weather_config.min_hours_to_expiry
            }
        }


async def run_weather_trading_strategy(
    db_manager: DatabaseManager,
    kalshi_client: KalshiClient,
    xai_client: XAIClient,
    config: Optional[WeatherTradingConfig] = None
) -> TradingSystemResults:
    """
    Main entry point for weather trading strategy.
    
    This function:
    1. Initializes the weather trading system
    2. Fetches real-time weather data
    3. Analyzes opportunities
    4. Executes trades
    5. Returns results
    """
    logger = get_trading_logger("weather_trading_main")
    
    try:
        logger.info("🌡️ Starting Weather Trading Strategy")
        
        # Initialize weather trading system
        weather_system = WeatherTradingStrategy(
            db_manager, kalshi_client, xai_client, config
        )
        
        # Initialize with dynamic balance (from parent class)
        await weather_system.async_initialize()
        
        # Execute weather trading strategy
        results = await weather_system.execute_weather_trading_strategy()
        
        # Log summary
        logger.info(
            f"🌡️ WEATHER TRADING COMPLETE 🌡️\n"
            f"📊 RESULTS:\n"
            f"  • Positions Created: {results.total_positions}\n"
            f"  • Capital Deployed: ${results.total_capital_used:.0f}\n"
            f"  • Expected Return: {results.portfolio_expected_return:.2%}\n"
            f"  • Sharpe Ratio: {results.portfolio_sharpe_ratio:.2f}\n"
            f"  • Portfolio Volatility: {results.portfolio_volatility:.2%}\n"
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Error in weather trading strategy: {e}")
        return TradingSystemResults()


# Test function
async def test_weather_trading():
    """Test weather trading strategy with mock data."""
    
    print("Testing Weather Trading Strategy...")
    print("=" * 60)
    
    # Would need proper mocks for full test
    # This is a simplified test structure
    
    from src.utils.database import DatabaseManager
    from src.clients.kalshi_client import KalshiClient
    from src.clients.xai_client import XAIClient
    
    # Initialize components (would need proper initialization)
    db_manager = DatabaseManager()
    kalshi_client = KalshiClient()
    xai_client = XAIClient()
    
    # Run strategy
    results = await run_weather_trading_strategy(
        db_manager, kalshi_client, xai_client
    )
    
    print(f"\n✅ Test Complete!")
    print(f"   Positions: {results.total_positions}")
    print(f"   Capital Used: ${results.total_capital_used:.0f}")
    print(f"   Expected Return: {results.portfolio_expected_return:.2%}")


if __name__ == "__main__":
    # Note: This would need proper environment setup to run
    print("Weather Trading Strategy Module")
    print("Use run_weather_trading_strategy() to execute")
