"""
Weather Intelligence Analyzer - Leverages existing XAIClient for analysis

This analyzer uses the existing LLM infrastructure to:
- Extract confidence from AFDs
- Analyze temperature trajectories
- Identify weather patterns
- Generate trading signals
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import json

import numpy as np
import pandas as pd

from src.clients.xai_client import XAIClient
from src.utils.logging_setup import get_trading_logger
from .noaa_client import (
    NOAAClient, 
    WeatherStation, 
    WeatherObservation, 
    AFDAnalysis,
    ModelForecast
)


@dataclass
class WeatherAnalysis:
    """Complete weather analysis for trading decision."""
    station_id: str
    market_ticker: str  # NHIGH, CHIHIGH, etc.
    current_temp: float
    forecast_high: float
    confidence_score: float  # 0-100
    edge: float  # Expected value vs market price
    trajectory: str  # 'rising', 'falling', 'stable'
    risk_factors: List[str]
    entry_recommendation: str  # 'BUY', 'SELL', 'HOLD'
    rationale: str
    model_consensus: Dict[str, float]
    meteorologist_confidence: float
    settlement_risk: str  # 'low', 'medium', 'high'


@dataclass
class TemperatureTrajectory:
    """Temperature trajectory analysis."""
    current: float
    rate_of_change: float  # degrees per hour
    time_to_peak: float  # hours
    expected_high: float
    confidence: float
    limiting_factors: List[str]


class WeatherAnalyzer:
    """
    Weather Intelligence System using existing LLM infrastructure.
    
    Leverages XAIClient for sophisticated analysis of weather data.
    """
    
    # Market mapping for Kalshi
    MARKET_MAPPING = {
        'KNYC': 'NHIGH',
        'KMIA': 'MIAHIGH', 
        'KMDW': 'CHIHIGH',
        'KDEN': 'DENHIGH',
        'KAUS': 'AUSHIGH',
        'KLAX': 'LAHIGH',
        'KPHL': 'PHILHIGH'
    }
    
    def __init__(self, xai_client: XAIClient, noaa_client: Optional[NOAAClient] = None):
        self.xai_client = xai_client
        self.noaa_client = noaa_client or NOAAClient()
        self.logger = get_trading_logger('weather_analyzer')
        
    async def analyze_weather_opportunity(
        self, 
        station_id: str,
        market_price: float,  # Current Kalshi market price
        strike_price: float   # Temperature threshold
    ) -> WeatherAnalysis:
        """
        Comprehensive weather analysis for trading decision.
        
        Args:
            station_id: Weather station (e.g., 'KNYC')
            market_price: Current Kalshi market price (0-1)
            strike_price: Temperature threshold for the market
        
        Returns:
            Complete weather analysis with trading recommendation
        """
        try:
            # Get current observation
            obs = await self.noaa_client.get_current_observation(station_id)
            if not obs:
                self.logger.error(f"No observation data for {station_id}")
                return self._create_error_analysis(station_id)
            
            # Get station metadata
            station = self.noaa_client.WEATHER_STATIONS.get(station_id)
            if not station:
                self.logger.error(f"Unknown station: {station_id}")
                return self._create_error_analysis(station_id)
            
            # Get AFD analysis
            afd = await self.noaa_client.get_afd(station.wfo_code)
            meteorologist_confidence = afd.confidence_score if afd else 60.0
            
            # Get model consensus
            model_consensus = await self.noaa_client.get_model_consensus(station, hour=12)
            
            # Analyze temperature trajectory
            trajectory = await self._analyze_trajectory(obs, station, model_consensus)
            
            # Use LLM for sophisticated analysis
            llm_analysis = await self._get_llm_analysis(
                obs, station, afd, model_consensus, trajectory, market_price, strike_price
            )
            
            # Calculate edge
            prob_exceed = self._calculate_probability(trajectory.expected_high, strike_price)
            edge = prob_exceed - market_price
            
            # Generate trading recommendation
            recommendation = self._generate_recommendation(edge, llm_analysis['confidence'])
            
            return WeatherAnalysis(
                station_id=station_id,
                market_ticker=self.MARKET_MAPPING.get(station_id, 'UNKNOWN'),
                current_temp=obs.temperature_f,
                forecast_high=trajectory.expected_high,
                confidence_score=llm_analysis['confidence'],
                edge=edge,
                trajectory=trajectory.rate_of_change > 0 and 'rising' or 'stable',
                risk_factors=llm_analysis.get('risk_factors', []),
                entry_recommendation=recommendation,
                rationale=llm_analysis.get('rationale', ''),
                model_consensus=model_consensus,
                meteorologist_confidence=meteorologist_confidence,
                settlement_risk=self._assess_settlement_risk(obs, trajectory)
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing weather opportunity: {e}")
            return self._create_error_analysis(station_id)
    
    async def _analyze_trajectory(
        self,
        obs: WeatherObservation,
        station: WeatherStation,
        model_consensus: Dict[str, float]
    ) -> TemperatureTrajectory:
        """
        Analyze temperature trajectory based on current conditions.
        """
        current_temp = obs.temperature_f
        current_hour = datetime.now().hour
        
        # Simple trajectory model (would be more sophisticated in production)
        # Consider time of day, sky conditions, wind
        hours_to_peak = self._estimate_hours_to_peak(station, current_hour)
        
        # Rate of change based on conditions
        if 'CLR' in obs.sky_conditions or 'FEW' in obs.sky_conditions:
            # Clear skies = stronger heating
            rate = 3.0  # degrees per hour
        elif 'OVC' in obs.sky_conditions:
            # Overcast = limited heating
            rate = 1.0
        else:
            rate = 2.0
        
        # Adjust for wind
        if obs.wind_speed > 15:
            rate *= 0.7  # Strong wind reduces heating
        
        # Expected high
        expected_high = current_temp + (rate * hours_to_peak)
        
        # Blend with model consensus
        if model_consensus:
            model_avg = np.mean(list(model_consensus.values()))
            expected_high = 0.6 * expected_high + 0.4 * model_avg
        
        # Limiting factors
        limiting_factors = []
        if obs.wind_speed > 15:
            limiting_factors.append('Strong winds')
        if 'OVC' in obs.sky_conditions:
            limiting_factors.append('Cloud cover')
        if hours_to_peak < 2:
            limiting_factors.append('Limited heating time')
        
        return TemperatureTrajectory(
            current=current_temp,
            rate_of_change=rate,
            time_to_peak=hours_to_peak,
            expected_high=expected_high,
            confidence=0.7 if len(limiting_factors) == 0 else 0.5,
            limiting_factors=limiting_factors
        )
    
    async def _get_llm_analysis(
        self,
        obs: WeatherObservation,
        station: WeatherStation,
        afd: Optional[AFDAnalysis],
        model_consensus: Dict[str, float],
        trajectory: TemperatureTrajectory,
        market_price: float,
        strike_price: float
    ) -> Dict:
        """
        Use existing LLM to analyze weather data.
        """
        # Prepare context for LLM
        context = f"""
        Analyze this weather trading opportunity:
        
        Station: {station.name} ({station.station_id})
        Current Temperature: {obs.temperature_f:.1f}°F
        Sky Conditions: {obs.sky_conditions}
        Wind: {obs.wind_speed} knots
        
        Temperature Trajectory:
        - Rate of Change: {trajectory.rate_of_change:.1f}°F/hour
        - Time to Peak: {trajectory.time_to_peak:.1f} hours
        - Expected High: {trajectory.expected_high:.1f}°F
        
        Model Consensus:
        {json.dumps(model_consensus, indent=2)}
        
        Market Information:
        - Strike Price: {strike_price}°F
        - Market Price: {market_price:.3f} (probability)
        - Market implies {market_price*100:.1f}% chance of exceeding {strike_price}°F
        
        Station Quirks: {', '.join(station.quirks)}
        
        {"AFD indicates: " + afd.pattern_type if afd else "No AFD available"}
        
        Provide:
        1. Confidence score (0-100) for reaching {strike_price}°F
        2. Key risk factors
        3. Trading rationale
        """
        
        try:
            # Use existing XAI client
            response = await self.xai_client.analyze_market(
                market_data={'context': context},
                historical_data=None
            )
            
            # Parse LLM response
            if response and 'content' in response:
                # Extract confidence (simplified - would parse properly)
                confidence = 70.0  # Default
                if 'high confidence' in response['content'].lower():
                    confidence = 85.0
                elif 'low confidence' in response['content'].lower():
                    confidence = 40.0
                
                return {
                    'confidence': confidence,
                    'risk_factors': self._extract_risk_factors(response['content']),
                    'rationale': response['content'][:500]  # First 500 chars
                }
            
        except Exception as e:
            self.logger.error(f"LLM analysis error: {e}")
        
        # Fallback to simple analysis
        return {
            'confidence': 60.0,
            'risk_factors': trajectory.limiting_factors,
            'rationale': f"Based on current conditions and trajectory analysis"
        }
    
    def _estimate_hours_to_peak(self, station: WeatherStation, current_hour: int) -> float:
        """Estimate hours until daily temperature peak."""
        # Peak times by timezone (simplified)
        peak_hours = {
            'America/New_York': 15,     # 3 PM
            'America/Chicago': 16,       # 4 PM
            'America/Denver': 14,        # 2 PM
            'America/Los_Angeles': 14    # 2 PM
        }
        
        peak_hour = peak_hours.get(station.timezone, 15)
        
        if current_hour >= peak_hour:
            return 0  # Already past peak
        
        return peak_hour - current_hour
    
    def _calculate_probability(self, expected_temp: float, strike: float) -> float:
        """Calculate probability of exceeding strike price."""
        # Simple normal distribution model
        # In production, would use historical distributions
        std_dev = 3.0  # Standard deviation in degrees
        
        from scipy.stats import norm
        z_score = (expected_temp - strike) / std_dev
        probability = norm.cdf(z_score)
        
        return min(max(probability, 0.01), 0.99)  # Bound between 1% and 99%
    
    def _generate_recommendation(self, edge: float, confidence: float) -> str:
        """Generate trading recommendation based on edge and confidence."""
        if confidence < 50:
            return 'HOLD'  # Too uncertain
        
        if abs(edge) < 0.05:
            return 'HOLD'  # Edge too small
        
        if edge > 0.10:
            return 'BUY'
        elif edge < -0.10:
            return 'SELL'
        else:
            return 'HOLD'
    
    def _assess_settlement_risk(self, obs: WeatherObservation, trajectory: TemperatureTrajectory) -> str:
        """Assess risk related to settlement data quality."""
        risks = []
        
        # Check for sensor issues
        if trajectory.current > 120 or trajectory.current < -40:
            risks.append('sensor_anomaly')
        
        # Check for missing data
        if not obs.twenty_four_hour_max:
            risks.append('missing_24hr_data')
        
        if len(risks) > 1:
            return 'high'
        elif len(risks) == 1:
            return 'medium'
        else:
            return 'low'
    
    def _extract_risk_factors(self, text: str) -> List[str]:
        """Extract risk factors from text."""
        risks = []
        
        risk_keywords = {
            'uncertainty': 'Model uncertainty',
            'cloud': 'Cloud cover impact',
            'wind': 'Wind effects',
            'front': 'Frontal passage',
            'storm': 'Storm potential',
            'marine': 'Marine layer influence'
        }
        
        text_lower = text.lower()
        for keyword, risk_description in risk_keywords.items():
            if keyword in text_lower:
                risks.append(risk_description)
        
        return risks[:5]  # Top 5 risks
    
    def _create_error_analysis(self, station_id: str) -> WeatherAnalysis:
        """Create error analysis when data unavailable."""
        return WeatherAnalysis(
            station_id=station_id,
            market_ticker=self.MARKET_MAPPING.get(station_id, 'UNKNOWN'),
            current_temp=0,
            forecast_high=0,
            confidence_score=0,
            edge=0,
            trajectory='unknown',
            risk_factors=['Data unavailable'],
            entry_recommendation='HOLD',
            rationale='Unable to analyze due to missing data',
            model_consensus={},
            meteorologist_confidence=0,
            settlement_risk='high'
        )
    
    async def analyze_all_markets(
        self, 
        market_prices: Dict[str, float]
    ) -> List[WeatherAnalysis]:
        """
        Analyze all weather markets simultaneously.
        
        Args:
            market_prices: Dict of station_id to market price
        
        Returns:
            List of weather analyses for all markets
        """
        analyses = []
        
        for station_id, market_ticker in self.MARKET_MAPPING.items():
            if station_id in market_prices:
                # Default strike prices (would get from Kalshi API)
                strike_prices = {
                    'KNYC': 85,
                    'KMIA': 90,
                    'KMDW': 85,
                    'KDEN': 90,
                    'KAUS': 100,
                    'KLAX': 80,
                    'KPHL': 85
                }
                
                analysis = await self.analyze_weather_opportunity(
                    station_id=station_id,
                    market_price=market_prices[station_id],
                    strike_price=strike_prices.get(station_id, 85)
                )
                
                analyses.append(analysis)
                
                self.logger.info(
                    f"📊 {market_ticker}: Current={analysis.current_temp:.1f}°F, "
                    f"Expected={analysis.forecast_high:.1f}°F, "
                    f"Edge={analysis.edge:.3f}, "
                    f"Recommendation={analysis.entry_recommendation}"
                )
        
        return analyses


# Test function
async def test_weather_analyzer():
    """Test weather analyzer functionality."""
    
    # Mock XAI client for testing
    class MockXAIClient:
        async def analyze_market(self, market_data, historical_data):
            return {
                'content': 'High confidence based on clear skies and rising temperatures. Risk factors include potential afternoon clouds.',
                'confidence': 75
            }
    
    print("Testing Weather Analyzer...")
    print("=" * 60)
    
    analyzer = WeatherAnalyzer(MockXAIClient())
    
    # Test single market analysis
    analysis = await analyzer.analyze_weather_opportunity(
        station_id='KNYC',
        market_price=0.45,  # Market thinks 45% chance
        strike_price=85     # Will it exceed 85°F?
    )
    
    print(f"\n📊 Weather Analysis for {analysis.market_ticker}:")
    print(f"   Current Temp: {analysis.current_temp:.1f}°F")
    print(f"   Forecast High: {analysis.forecast_high:.1f}°F")
    print(f"   Confidence: {analysis.confidence_score:.0f}%")
    print(f"   Edge: {analysis.edge:.3f}")
    print(f"   Recommendation: {analysis.entry_recommendation}")
    print(f"   Risk Factors: {', '.join(analysis.risk_factors)}")
    
    # Test all markets
    print("\n" + "=" * 60)
    print("Analyzing All Markets...")
    print("-" * 60)
    
    mock_prices = {
        'KNYC': 0.50,
        'KMIA': 0.65,
        'KMDW': 0.45,
        'KDEN': 0.40,
        'KAUS': 0.70,
        'KLAX': 0.55,
        'KPHL': 0.48
    }
    
    all_analyses = await analyzer.analyze_all_markets(mock_prices)
    
    # Summary
    buy_signals = sum(1 for a in all_analyses if a.entry_recommendation == 'BUY')
    sell_signals = sum(1 for a in all_analyses if a.entry_recommendation == 'SELL')
    
    print(f"\n📈 Summary:")
    print(f"   BUY signals: {buy_signals}")
    print(f"   SELL signals: {sell_signals}")
    print(f"   HOLD signals: {len(all_analyses) - buy_signals - sell_signals}")
    
    print("\n✅ Weather Analyzer test complete!")


if __name__ == "__main__":
    asyncio.run(test_weather_analyzer())
