# Weather Trading Bot - Secure Configuration Template
# COPY THIS TO .env AND FILL IN YOUR ACTUAL VALUES
# NEVER COMMIT THE .env FILE TO VERSION CONTROL!

# ============================================
# KALSHI CREDENTIALS (REQUIRED)
# ============================================
KALSHI_EMAIL=<EMAIL>
KALSHI_PASSWORD=your_secure_password_here
KALSHI_API_KEY=your_kalshi_api_key_here

# For production, use separate credentials
KALSHI_PROD_EMAIL=<EMAIL>
KALSHI_PROD_PASSWORD=production_password_here
KALSHI_PROD_API_KEY=production_api_key_here

# ============================================
# LLM API KEYS (REQUIRED FOR AI FEATURES)
# ============================================
# The kalshi-ai-trading-bot uses xAI (Grok) as primary LLM
# Get your xAI API key from: https://x.ai/api
XAI_API_KEY=your_xai_api_key_here

# Optional: OpenAI as fallback (repository includes OpenAI client)
OPENAI_API_KEY=sk-your_openai_api_key_here

# Note: Anthropic is NOT used by the kalshi-ai-trading-bot
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ============================================
# WEATHER API KEYS (OPTIONAL - Most are FREE)
# ============================================
# NOAA APIs are free and don't require keys
# If you get enhanced access, add keys here:
NOAA_TOKEN=optional_noaa_token
NWS_API_KEY=optional_nws_key

# ============================================
# DATABASE ENCRYPTION (AUTO-GENERATED)
# ============================================
# This will be auto-generated on first run
# Used to encrypt sensitive data in database
DB_ENCRYPTION_KEY=

# ============================================
# SECURITY SETTINGS
# ============================================
# Set to 'production' for live trading
ENVIRONMENT=development

# Enable additional security checks
ENABLE_SECURITY_CHECKS=true

# IP whitelist (comma-separated)
ALLOWED_IPS=127.0.0.1,localhost

# Session timeout in minutes
SESSION_TIMEOUT=30

# ============================================
# TRADING LIMITS (SAFETY)
# ============================================
MAX_DAILY_TRADES=50
MAX_POSITION_SIZE=1000
MAX_DAILY_LOSS=500

# ============================================
# LOGGING
# ============================================
LOG_LEVEL=INFO
LOG_FILE=weather_bot.log
LOG_SENSITIVE_DATA=false

# ============================================
# NOTES
# ============================================
# 1. Keep this file secure and never share it
# 2. Use strong, unique passwords
# 3. Rotate API keys regularly
# 4. Monitor for unauthorized access
# 5. Use read-only API keys where possible
